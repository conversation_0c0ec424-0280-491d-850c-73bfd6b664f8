include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.config.dart"
    - "**/*.gr.dart"
    - "**/*.mocks.dart"
    # Exclude Mason bricks templates from analysis
    - "bricks/**"
    - "**/bricks/**"
    - "**/__brick__/**"
  errors:
    # 临时忽略 JsonKey 注解错误，升级到 Freezed 3.x 后将自动解决
    invalid_annotation_target: ignore

linter:
  rules:
    # 废弃 API 检测
    - deprecated_member_use_from_same_package

    # Material Design 3 最佳实践
    - use_colored_box
    - use_decorated_box

    # 性能优化
    - avoid_unnecessary_containers
    - sized_box_for_whitespace
    - prefer_const_constructors
    - prefer_const_literals_to_create_immutables

    # Freezed 3.x 最佳实践
    - prefer_final_fields
    - avoid_redundant_argument_values

