# Build configuration for code generation
targets:
  $default:
    builders:
      # Freezed 3.x 全局配置
      freezed:
        options:
          # 默认 union 配置
          union_key: runtimeType
          union_value_case: snake
          # 启用通用参数工厂（如果需要）
          # generic_argument_factories: true
          
      # JSON 序列化配置
      json_serializable:
        options:
          # 生成更严格的 JSON 序列化代码
          explicit_to_json: true
          include_if_null: false
          
      # Injectable 依赖注入配置
      injectable_generator:
        options:
          # 自动注册配置
          auto_register: true
          
      # Retrofit API 客户端配置
      retrofit_generator:
        options:
          # 生成更详细的错误处理
          generate_to_json: true
