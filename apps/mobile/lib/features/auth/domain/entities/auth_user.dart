import 'package:data_models/data_models.dart';

typedef AuthUser = User;

extension AuthUserExtensions on User {
  /// Create AuthUser from API response with tokens
  static User fromAuthResponse({
    required String id,
    required String email,
    String? name,
    String? avatar,
    String? accessToken,
    String? refreshToken,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return User(
      id: id,
      email: email,
      name: name,
      avatar: avatar,
      createdAt: createdAt,
      updatedAt: updatedAt,
      accessToken: accessToken,
      refreshToken: refreshToken,
    );
  }
}