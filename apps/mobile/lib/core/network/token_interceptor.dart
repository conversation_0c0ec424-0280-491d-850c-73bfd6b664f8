import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../shared/constants.dart';

class TokenInterceptor extends Interceptor {
  final SharedPreferences _prefs;
  final Dio _dio;
  
  TokenInterceptor(this._prefs, this._dio);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final token = _prefs.getString(AppConstants.accessTokenKey);
    if (token != null && token.isNotEmpty) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      // Token expired, try to refresh
      final refreshed = await _refreshToken();
      if (refreshed) {
        // Retry the original request
        final clonedRequest = await _retryRequest(err.requestOptions);
        handler.resolve(clonedRequest);
        return;
      } else {
        // Refresh failed, redirect to login
        await _clearTokens();
      }
    }
    handler.next(err);
  }

  Future<bool> _refreshToken() async {
    try {
      final refreshToken = _prefs.getString(AppConstants.refreshTokenKey);
      if (refreshToken == null) return false;

      final response = await _dio.post(
        '/auth/refresh',
        data: {'refresh_token': refreshToken},
        options: Options(
          headers: {'Authorization': null}, // Don't include old token
        ),
      );

      if (response.statusCode == 200) {
        final newAccessToken = response.data['access_token'];
        final newRefreshToken = response.data['refresh_token'];
        
        await _prefs.setString(AppConstants.accessTokenKey, newAccessToken);
        if (newRefreshToken != null) {
          await _prefs.setString(AppConstants.refreshTokenKey, newRefreshToken);
        }
        
        return true;
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Token refresh failed: $e');
      }
    }
    return false;
  }

  Future<Response<dynamic>> _retryRequest(RequestOptions requestOptions) async {
    final token = _prefs.getString(AppConstants.accessTokenKey);
    if (token != null) {
      requestOptions.headers['Authorization'] = 'Bearer $token';
    }
    
    return await _dio.request(
      requestOptions.path,
      data: requestOptions.data,
      queryParameters: requestOptions.queryParameters,
      options: Options(
        method: requestOptions.method,
        headers: requestOptions.headers,
      ),
    );
  }

  Future<void> _clearTokens() async {
    await _prefs.remove(AppConstants.accessTokenKey);
    await _prefs.remove(AppConstants.refreshTokenKey);
  }
}