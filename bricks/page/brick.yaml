name: page
description: Creates a new page with optional BLoC integration
version: 0.1.0

vars:
  name:
    type: string
    description: Page name
    default: example
    prompt: What is the name of the page?
  
  feature:
    type: string
    description: Feature name
    default: core
    prompt: What feature does this page belong to?
  
  bloc:
    type: boolean
    description: Include BLoC?
    default: true
    prompt: Do you want to include BLoC integration?
  
  entity:
    type: string
    description: Entity name (if using BLoC)
    default: ""
    prompt: What entity does this page work with? (if using BLoC)