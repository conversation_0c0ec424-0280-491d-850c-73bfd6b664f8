import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:flutter/material.dart';

class AppTheme {
  const AppTheme._();

  static ThemeData lightTheme = FlexThemeData.light(
    colors: const FlexSchemeColor(
      primary: Color(0xFF2A6BFF),
      primaryContainer: Color(0xFFD0E4FF),
      secondary: Color(0xFF006875),
      secondaryContainer: Color(0xFF95F0FF),
      tertiary: Color(0xFF006874),
      tertiaryContainer: Color(0xFF97F0FF),
      appBarColor: Color(0xFF95F0FF),
      error: Color(0xFFBA1A1A),
    ),
    surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
    blendLevel: 7,
    subThemesData: const FlexSubThemesData(
      blendOnLevel: 10,
      blendOnColors: false,
      useMaterial3Typography: true,
      useM2StyleDividerInM3: true,
      alignedDropdown: true,
      useInputDecoratorThemeInDialogs: true,
    ),
    visualDensity: FlexColorScheme.comfortablePlatformDensity,
    useMaterial3: true,
    swapLegacyOnMaterial3: true,
    fontFamily: 'Inter',
  );

  static ThemeData darkTheme = FlexThemeData.dark(
    colors: const FlexSchemeColor(
      primary: Color(0xFFAEC6FF),
      primaryContainer: Color(0xFF00497C),
      secondary: Color(0xFF4FD8EB),
      secondaryContainer: Color(0xFF004E59),
      tertiary: Color(0xFF4FD8EB),
      tertiaryContainer: Color(0xFF004F5A),
      appBarColor: Color(0xFF004E59),
      error: Color(0xFFFFB4AB),
    ),
    surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
    blendLevel: 13,
    subThemesData: const FlexSubThemesData(
      blendOnLevel: 20,
      useMaterial3Typography: true,
      useM2StyleDividerInM3: true,
      alignedDropdown: true,
      useInputDecoratorThemeInDialogs: true,
    ),
    visualDensity: FlexColorScheme.comfortablePlatformDensity,
    useMaterial3: true,
    swapLegacyOnMaterial3: true,
    fontFamily: 'Inter',
  );
}