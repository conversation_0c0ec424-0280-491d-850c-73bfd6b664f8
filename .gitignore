# Flutter/Dart related
**/doc/api/
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
build/
flutter_*.png
linked_*.ds
unlinked.ds
unlinked_spec.ds

flutter_create_app/*

# Coverage
coverage/

# Mason bricks - keep templates but ignore generated files
# Note: __brick__ directories contain templates with Mason syntax
# They may show Dart analysis errors but should be committed

# Melos
.melos_tool/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Environment
.env
.env.local
.env.development
.env.staging
.env.production

# Firebase
**/ios/Runner/GoogleService-Info.plist
**/android/app/google-services.json
.firebase/

# Fastlane
**/ios/fastlane/report.xml
**/ios/fastlane/Preview.html
**/ios/fastlane/screenshots
**/ios/fastlane/test_output
**/android/fastlane/report.xml
**/android/fastlane/metadata

# Shorebird
shorebird.yaml

### AI
#.cursor/**
.specstory/**
.cursor*
.chat
.cunzhi*
.augment*
.claude*
CLAUDE.md
