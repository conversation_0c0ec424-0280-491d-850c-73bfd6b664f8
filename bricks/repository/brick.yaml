name: repository
description: Creates a new repository interface and implementation
version: 0.1.0

vars:
  name:
    type: string
    description: Repository name
    default: example
    prompt: What is the name of the repository?
  
  feature:
    type: string
    description: Feature name
    default: core
    prompt: What feature does this repository belong to?
  
  model:
    type: string
    description: Model name
    default: ""
    prompt: What is the name of the model this repository handles?