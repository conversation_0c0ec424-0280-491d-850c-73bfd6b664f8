import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

/// 应用统一日志管理器
/// 
/// 功能特性：
/// - 环境自适应日志级别
/// - 结构化日志输出
/// - 性能优化的生产环境配置
/// - 支持不同日志类型（调试、信息、警告、错误）
class AppLogger {
  static AppLogger? _instance;
  late final Logger _logger;

  AppLogger._internal() {
    _logger = Logger(
      filter: _AppLogFilter(),
      printer: _getLogPrinter(),
      output: _getLogOutput(),
    );
  }

  /// 获取单例实例
  static AppLogger get instance {
    _instance ??= AppLogger._internal();
    return _instance!;
  }

  /// 获取适合当前环境的日志打印器
  LogPrinter _getLogPrinter() {
    if (kDebugMode) {
      // 开发环境：使用美化打印器，包含完整信息
      return PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
      );
    } else {
      // 生产环境：使用简洁打印器，减少性能开销
      return SimplePrinter(colors: false);
    }
  }

  /// 获取适合当前环境的日志输出器
  LogOutput _getLogOutput() {
    if (kReleaseMode) {
      // 生产环境：可以集成到崩溃报告系统
      return _ProductionLogOutput();
    } else {
      // 开发/测试环境：输出到控制台
      return ConsoleOutput();
    }
  }

  // === 公共日志方法 ===

  /// 调试日志 - 仅在开发环境显示
  void debug(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.d(message, error: error, stackTrace: stackTrace);
  }

  /// 信息日志 - 重要的业务流程信息
  void info(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// 警告日志 - 需要注意但不影响功能的问题
  void warning(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  /// 错误日志 - 影响功能的错误
  void error(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// 致命错误日志 - 导致应用崩溃的严重错误
  void fatal(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.f(message, error: error, stackTrace: stackTrace);
  }

  // === 业务场景专用方法 ===

  /// 网络请求日志
  void network(String operation, {String? url, int? statusCode, dynamic error}) {
    final message = '🌐 Network: $operation';
    final details = <String>[];
    if (url != null) details.add('URL: $url');
    if (statusCode != null) details.add('Status: $statusCode');
    
    final fullMessage = details.isEmpty 
        ? message 
        : '$message\n${details.join('\n')}';
    
    if (error != null) {
      this.error(fullMessage, error);
    } else {
      info(fullMessage);
    }
  }

  /// 认证相关日志
  void auth(String operation, {bool success = true, String? userId, dynamic error}) {
    final message = '🔐 Auth: $operation';
    final details = <String>[];
    details.add('Success: $success');
    if (userId != null) details.add('User: $userId');
    
    final fullMessage = '$message\n${details.join('\n')}';
    
    if (error != null) {
      this.error(fullMessage, error);
    } else if (success) {
      info(fullMessage);
    } else {
      warning(fullMessage);
    }
  }

  /// 数据库操作日志
  void database(String operation, {String? table, dynamic error}) {
    final message = '💾 Database: $operation';
    final details = <String>[];
    if (table != null) details.add('Table: $table');
    
    final fullMessage = details.isEmpty 
        ? message 
        : '$message\n${details.join('\n')}';
    
    if (error != null) {
      this.error(fullMessage, error);
    } else {
      debug(fullMessage);
    }
  }

  /// UI 交互日志
  void ui(String action, {String? screen, Map<String, dynamic>? data}) {
    final message = '🎨 UI: $action';
    final details = <String>[];
    if (screen != null) details.add('Screen: $screen');
    if (data != null) details.add('Data: $data');
    
    final fullMessage = details.isEmpty 
        ? message 
        : '$message\n${details.join('\n')}';
    
    debug(fullMessage);
  }
}

/// 自定义日志过滤器
/// 根据环境和日志级别决定是否输出日志
class _AppLogFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    if (kReleaseMode) {
      // 生产环境：只记录警告及以上级别
      return event.level.index >= Level.warning.index;
    } else if (kProfileMode) {
      // 性能测试环境：记录信息及以上级别
      return event.level.index >= Level.info.index;
    } else {
      // 开发环境：记录所有级别
      return true;
    }
  }
}

/// 生产环境日志输出器
/// 可以集成到崩溃报告系统或远程日志服务
class _ProductionLogOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    // 在生产环境中，可以将日志发送到：
    // 1. Firebase Crashlytics
    // 2. Sentry
    // 3. 自定义日志服务
    // 4. 本地文件存储
    
    // 目前只在严重错误时输出到控制台
    if (event.level.index >= Level.error.index) {
      for (var line in event.lines) {
        // ignore: avoid_print
        print(line);
      }
    }
  }
}

/// 全局日志实例，方便在整个应用中使用
final appLogger = AppLogger.instance;
