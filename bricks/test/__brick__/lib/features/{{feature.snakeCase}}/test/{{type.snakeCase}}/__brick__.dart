// Helper file for conditional logic in templates
bool get isUnit => type.snakeCase == 'unit';
bool get isWidget => type.snakeCase == 'widget';
bool get isIntegration => type.snakeCase == 'integration';
bool get isUseCase => name.snakeCase.contains('usecase') || name.snakeCase.contains('use_case');
bool get isRepository => name.snakeCase.contains('repository');
bool get usesBloc => name.snakeCase.contains('bloc') || name.snakeCase.contains('page');