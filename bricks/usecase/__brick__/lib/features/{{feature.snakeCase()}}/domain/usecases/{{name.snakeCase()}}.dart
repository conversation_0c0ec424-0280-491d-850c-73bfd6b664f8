import 'package:dartz/dartz.dart';
import '../../../../core/error/failure.dart';
import '../repositories/{{repository.snakeCase()}}_repository.dart';
import '../entities/{{entity.snakeCase()}}.dart';

class {{name.pascalCase()}} {
  final {{repository.pascalCase()}}Repository repository;

  {{name.pascalCase()}}(this.repository);

  Future<Either<Failure, {{entity.pascalCase()}}>> call({{entity.pascalCase()}} {{entity.camelCase()}}) async {
    try {
      final result = await repository.create{{entity.pascalCase()}}({{entity.camelCase()}});
      return Right(result);
    } on Exception catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}