# Clean Architecture终极指南 - 从理论到实战

## 🎯 写给所有架构师的Clean Architecture圣经

> **什么是Clean Architecture？**
> 
> Clean Architecture就像一座精心设计的城市：有明确的区域划分（商业区、住宅区、工业区），每个区域有清晰的边界和职责。你不会在住宅区建工厂，也不会在商业区种菜。软件架构也是如此——每一层都有明确的职责，层与层之间有清晰的边界，让系统易于理解、维护和扩展。

---

## 第一章：架构的哲学 - 为什么要Clean Architecture？

### 🏗️ 传统架构的痛点

#### 1. 意大利面条式代码 - 当系统变成一团乱麻

```dart
// ❌ 传统做法：所有逻辑混在一起
class UserController {
  // 数据库操作和UI逻辑混合
  Future<void> login(String email, String password) async {
    // 1. UI验证逻辑
    if (email.isEmpty || password.isEmpty) {
      showError("请输入邮箱和密码");
      return;
    }
    
    // 2. 网络请求逻辑
    final response = await http.post(
      Uri.parse('https://api.example.com/login'),
      body: {'email': email, 'password': password}
    );
    
    // 3. 数据库操作
    final db = await openDatabase('app.db');
    await db.insert('users', {'email': email, 'token': response.body});
    
    // 4. UI更新
    Navigator.pushReplacementNamed(context, '/home');
  }
  
  // 问题：
  // - 无法单元测试（需要真实数据库和网络）
  // - UI变化会影响业务逻辑
  // - 数据库变化会影响UI
  // - 无法复用业务逻辑
}
```

#### 2. 业务逻辑的孤岛 - 当需求变化成为噩梦

想象一个电商平台：
- 用户可以在APP下单
- 商家可以在后台管理订单
- 管理员可以查看统计数据

如果业务逻辑散落在各个地方，当"满100减20"的促销规则变化时，你需要修改多少个地方？

#### 3. 测试的困境 - 为什么测试这么难？

```dart
// ❌ 难以测试的代码
class OrderService {
  Future<double> calculatePrice(List<Item> items) async {
    // 直接依赖数据库
    final db = await openDatabase('shop.db');
    final userLevel = await db.query('users', where: 'id = ?', whereArgs: [userId]);
    
    // 直接依赖网络
    final exchangeRate = await http.get(Uri.parse('https://api.exchangerate.com/latest'));
    
    // 计算逻辑和外部依赖混合
    double total = items.fold(0, (sum, item) => sum + item.price);
    if (userLevel.first['level'] == 'VIP') {
      total *= 0.9;  // VIP 9折
    }
    total *= exchangeRate.body;  // 汇率转换
    
    return total;
  }
}

// 测试这个函数需要：
// 1. 真实的数据库
// 2. 真实的网络连接
// 3. 真实的外部API
// 根本无法单元测试！
```

### 🎯 Clean Architecture的救赎

#### 1. 业务逻辑的独立性

```dart
// ✅ Clean Architecture：业务逻辑独立
class CalculateOrderPrice {
  final UserRepository userRepository;
  final ExchangeRateService exchangeRateService;
  
  CalculateOrderPrice(this.userRepository, this.exchangeRateService);
  
  Future<double> execute(List<Item> items, String userId) async {
    // 纯业务逻辑，不依赖任何框架
    final user = await userRepository.getUser(userId);
    final exchangeRate = await exchangeRateService.getRate();
    
    var total = items.fold(0.0, (sum, item) => sum + item.price);
    
    if (user.isVip) {
      total *= 0.9;
    }
    
    return total * exchangeRate;
  }
}

// 现在可以轻松测试：
// - Mock UserRepository
// - Mock ExchangeRateService
// - 测试纯业务逻辑
```

#### 2. 真实的商业案例 - 阿里巴巴的订单系统重构

**重构前：**
- 订单状态变更逻辑散落在12个不同文件中
- 每次修改促销规则需要3-5天
- 新功能开发平均需要2周

**重构后（Clean Architecture）：
- 订单状态机集中在一个领域模型中
- 促销规则修改只需2-4小时
- 新功能开发缩短到3-5天

### 🏛️ Clean Architecture的核心原则

#### 1. 依赖倒置原则（DIP）

```dart
// ❌ 高层模块依赖低层模块
class OrderController {
  final SqlOrderRepository repository;  // 直接依赖具体实现
  
  Future<void> createOrder(Order order) async {
    await repository.save(order);  // 耦合数据库
  }
}

// ✅ 高层模块依赖抽象
class OrderController {
  final OrderRepository repository;  // 依赖抽象接口
  
  Future<void> createOrder(Order order) async {
    await repository.save(order);  // 不依赖具体实现
  }
}

abstract class OrderRepository {
  Future<void> save(Order order);
  Future<Order?> findById(String id);
}
```

#### 2. 单一职责原则（SRP）

每个类只有一个改变的理由：
- `Order` - 表示订单这个业务概念
- `OrderRepository` - 负责订单的持久化
- `OrderService` - 负责订单的业务逻辑
- `OrderController` - 负责处理HTTP请求

#### 3. 开闭原则（OCP）

```dart
// ✅ 对扩展开放，对修改封闭
abstract class DiscountStrategy {
  double calculateDiscount(Order order);
}

class VipDiscount implements DiscountStrategy {
  @override
  double calculateDiscount(Order order) => order.total * 0.1;
}

class NewUserDiscount implements DiscountStrategy {
  @override
  double calculateDiscount(Order order) => order.total * 0.2;
}

// 新增促销无需修改现有代码
class DoubleElevenDiscount implements DiscountStrategy {
  @override
  double calculateDiscount(Order order) => order.total * 0.5;
}
```

---

## 第二章：Clean Architecture的四层架构

### 🏗️ 架构层次详解

```
┌─────────────────────────────────────────┐
│              用户界面层                    │
│        (Presentation Layer)              │
│  Widgets → Controllers → ViewModels     │
├─────────────────────────────────────────┤
│              应用层                       │
│        (Application Layer)               │
│  Use Cases → Application Services        │
├─────────────────────────────────────────┤
│              领域层                       │
│         (Domain Layer)                   │
│  Entities → Value Objects → Repository   │
│         → Domain Services                │
├─────────────────────────────────────────┤
│              基础设施层                   │
│       (Infrastructure Layer)             │
│  Repositories → Data Sources             │
│  → External Services                     │
└─────────────────────────────────────────┘
```

### 1. 领域层（Domain Layer）- 业务的心脏

#### 实体（Entities）- 业务的核心概念

```dart
// lib/features/orders/domain/entities/order.dart

class Order extends Equatable {
  final OrderId id;
  final UserId userId;
  final List<OrderItem> items;
  final OrderStatus status;
  final DateTime createdAt;
  final DateTime? paidAt;
  final Money totalAmount;
  
  const Order({
    required this.id,
    required this.userId,
    required this.items,
    this.status = OrderStatus.pending,
    required this.createdAt,
    this.paidAt,
    required this.totalAmount,
  });
  
  // 业务逻辑：添加商品
  Order addItem(Product product, int quantity) {
    final newItem = OrderItem(
      productId: product.id,
      name: product.name,
      price: product.price,
      quantity: quantity,
    );
    
    final newItems = [...items, newItem];
    final newTotal = totalAmount + (product.price * quantity);
    
    return copyWith(
      items: newItems,
      totalAmount: newTotal,
    );
  }
  
  // 业务逻辑：取消订单
  Order cancel() {
    if (status == OrderStatus.shipped) {
      throw Exception('已发货的订单不能取消');
    }
    
    return copyWith(status: OrderStatus.cancelled);
  }
  
  // 业务逻辑：支付订单
  Order pay(DateTime paymentTime) {
    if (status != OrderStatus.pending) {
      throw Exception('只能支付待付款订单');
    }
    
    return copyWith(
      status: OrderStatus.paid,
      paidAt: paymentTime,
    );
  }
  
  @override
  List<Object?> get props => [id, userId, items, status, createdAt, paidAt, totalAmount];
}

// 值对象：订单ID
class OrderId extends ValueObject {
  final String value;
  
  factory OrderId(String value) {
    if (value.isEmpty) {
      throw ArgumentError('订单ID不能为空');
    }
    return OrderId._(value);
  }
  
  const OrderId._(this.value);
  
  @override
  List<Object?> get props => [value];
}

// 枚举：订单状态
enum OrderStatus {
  pending,    // 待付款
  paid,       // 已付款
  shipped,    // 已发货
  delivered,  // 已送达
  cancelled,  // 已取消
}

// 值对象：金额
class Money extends ValueObject {
  final double amount;
  final String currency;
  
  factory Money(double amount, {String currency = 'CNY'}) {
    if (amount < 0) {
      throw ArgumentError('金额不能为负数');
    }
    return Money._(amount, currency);
  }
  
  const Money._(this.amount, this.currency);
  
  Money operator +(Money other) {
    if (currency != other.currency) {
      throw ArgumentError('货币类型不匹配');
    }
    return Money(amount + other.amount, currency: currency);
  }
  
  Money operator *(int quantity) {
    return Money(amount * quantity, currency: currency);
  }
  
  @override
  List<Object?> get props => [amount, currency];
}
```

#### 仓库接口（Repository Interfaces）- 抽象的数据访问

```dart
// lib/features/orders/domain/repositories/order_repository.dart

abstract class OrderRepository {
  // 根据ID查找订单
  Future<Order?> findById(OrderId id);
  
  // 保存订单
  Future<void> save(Order order);
  
  // 查找用户的订单
  Future<List<Order>> findByUserId(UserId userId);
  
  // 查找待支付订单
  Future<List<Order>> findPendingOrders();
  
  // 更新订单状态
  Future<void> updateStatus(OrderId id, OrderStatus status);
}

// 领域服务：当业务逻辑涉及多个实体时
class OrderPricingService {
  final ExchangeRateService exchangeRateService;
  final TaxService taxService;
  final DiscountService discountService;
  
  OrderPricingService({
    required this.exchangeRateService,
    required this.taxService,
    required this.discountService,
  });
  
  Future<Money> calculateFinalPrice(Order order, User user) async {
    var total = order.totalAmount;
    
    // 应用折扣
    final discount = await discountService.calculateDiscount(order, user);
    total = total - discount;
    
    // 计算税费
    final tax = await taxService.calculateTax(total);
    total = total + tax;
    
    // 汇率转换
    final rate = await exchangeRateService.getRate('CNY', 'USD');
    total = Money(total.amount * rate, currency: 'USD');
    
    return total;
  }
}
```

### 2. 应用层（Application Layer）- 用例的编排

#### 用例（Use Cases）- 应用的核心操作

```dart
// lib/features/orders/application/use_cases/create_order_use_case.dart

class CreateOrderUseCase {
  final OrderRepository orderRepository;
  final UserRepository userRepository;
  final ProductRepository productRepository;
  final OrderPricingService pricingService;
  final EventBus eventBus;
  
  CreateOrderUseCase({
    required this.orderRepository,
    required this.userRepository,
    required this.productRepository,
    required this.pricingService,
    required this.eventBus,
  });
  
  Future<Order> execute(CreateOrderCommand command) async {
    // 1. 验证用户
    final user = await userRepository.findById(command.userId);
    if (user == null) {
      throw UserNotFoundException();
    }
    
    // 2. 验证商品
    final products = <Product>[];
    for (final item in command.items) {
      final product = await productRepository.findById(item.productId);
      if (product == null) {
        throw ProductNotFoundException(item.productId);
      }
      if (product.stock < item.quantity) {
        throw InsufficientStockException(product.id);
      }
      products.add(product);
    }
    
    // 3. 创建订单实体
    final orderId = OrderId(UniqueId.generate());
    final items = <OrderItem>[];
    var totalAmount = Money(0);
    
    for (var i = 0; i < products.length; i++) {
      final product = products[i];
      final commandItem = command.items[i];
      
      items.add(OrderItem(
        productId: product.id,
        name: product.name,
        price: product.price,
        quantity: commandItem.quantity,
      ));
      
      totalAmount = totalAmount + (product.price * commandItem.quantity);
    }
    
    final order = Order(
      id: orderId,
      userId: user.id,
      items: items,
      totalAmount: totalAmount,
      createdAt: DateTime.now(),
    );
    
    // 4. 保存订单
    await orderRepository.save(order);
    
    // 5. 发布领域事件
    await eventBus.publish(OrderCreatedEvent(order));
    
    return order;
  }
}

// 命令对象：用例的输入参数
class CreateOrderCommand {
  final UserId userId;
  final List<OrderItemCommand> items;
  final String? couponCode;
  final Address? shippingAddress;
  
  const CreateOrderCommand({
    required this.userId,
    required this.items,
    this.couponCode,
    this.shippingAddress,
  });
}

class OrderItemCommand {
  final ProductId productId;
  final int quantity;
  
  const OrderItemCommand({
    required this.productId,
    required this.quantity,
  });
}
```

#### 应用服务（Application Services）- 跨用例的协调

```dart
// lib/features/orders/application/services/order_service.dart

class OrderApplicationService {
  final CreateOrderUseCase createOrderUseCase;
  final CancelOrderUseCase cancelOrderUseCase;
  final PayOrderUseCase payOrderUseCase;
  final GetOrderDetailsUseCase getOrderDetailsUseCase;
  
  OrderApplicationService({
    required this.createOrderUseCase,
    required this.cancelOrderUseCase,
    required this.payOrderUseCase,
    required this.getOrderDetailsUseCase,
  });
  
  // 复杂的业务流程
  Future<Order> completeOrderProcess(OrderProcessCommand command) async {
    // 1. 创建订单
    final order = await createOrderUseCase.execute(command.createOrderCommand);
    
    // 2. 应用优惠券（如果有）
    if (command.couponCode != null) {
      await applyCouponUseCase.execute(ApplyCouponCommand(
        orderId: order.id,
        couponCode: command.couponCode!,
      ));
    }
    
    // 3. 选择配送方式
    if (command.shippingMethod != null) {
      await selectShippingUseCase.execute(SelectShippingCommand(
        orderId: order.id,
        shippingMethod: command.shippingMethod!,
      ));
    }
    
    // 4. 支付订单
    final paidOrder = await payOrderUseCase.execute(PayOrderCommand(
      orderId: order.id,
      paymentMethod: command.paymentMethod,
    ));
    
    return paidOrder;
  }
}
```

### 3. 基础设施层（Infrastructure Layer）- 技术的实现细节

#### 数据仓库实现（Repository Implementations）

```dart
// lib/features/orders/data/repositories/order_repository_impl.dart

class OrderRepositoryImpl implements OrderRepository {
  final OrderRemoteDataSource remoteDataSource;
  final OrderLocalDataSource localDataSource;
  final NetworkInfo networkInfo;
  
  OrderRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });
  
  @override
  Future<Order?> findById(OrderId id) async {
    try {
      // 优先从本地获取
      final localOrder = await localDataSource.getOrder(id.value);
      if (localOrder != null) {
        return localOrder.toDomain();
      }
      
      // 本地没有，从远程获取
      if (await networkInfo.isConnected) {
        final remoteOrder = await remoteDataSource.getOrder(id.value);
        if (remoteOrder != null) {
          // 缓存到本地
          await localDataSource.saveOrder(remoteOrder);
          return remoteOrder.toDomain();
        }
      }
      
      return null;
    } catch (e) {
      throw DataSourceException(e.toString());
    }
  }
  
  @override
  Future<void> save(Order order) async {
    try {
      final orderModel = order.toModel();
      
      // 保存到本地
      await localDataSource.saveOrder(orderModel);
      
      // 如果有网络，同步到远程
      if (await networkInfo.isConnected) {
        await remoteDataSource.createOrder(orderModel);
      } else {
        // 标记为需要同步
        await localDataSource.markForSync(order.id.value);
      }
    } catch (e) {
      throw DataSourceException(e.toString());
    }
  }
  
  @override
  Future<List<Order>> findByUserId(UserId userId) async {
    try {
      if (await networkInfo.isConnected) {
        // 从远程获取最新数据
        final orders = await remoteDataSource.getUserOrders(userId.value);
        
        // 更新本地缓存
        await localDataSource.saveOrders(orders);
        
        return orders.map((order) => order.toDomain()).toList();
      } else {
        // 使用本地缓存
        final orders = await localDataSource.getUserOrders(userId.value);
        return orders.map((order) => order.toDomain()).toList();
      }
    } catch (e) {
      throw DataSourceException(e.toString());
    }
  }
}
```

#### 数据源实现（Data Sources）

```dart
// lib/features/orders/data/datasources/order_remote_data_source.dart

class OrderRemoteDataSourceImpl implements OrderRemoteDataSource {
  final Dio client;
  final AuthInterceptor authInterceptor;
  
  OrderRemoteDataSourceImpl({
    required this.client,
    required this.authInterceptor,
  }) {
    client.interceptors.add(authInterceptor);
  }
  
  @override
  Future<OrderModel?> getOrder(String id) async {
    try {
      final response = await client.get('/orders/$id');
      
      if (response.statusCode == 200) {
        return OrderModel.fromJson(response.data);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw ServerException('Failed to get order: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw ServerException(e.message ?? 'Unknown error');
    }
  }
  
  @override
  Future<List<OrderModel>> getUserOrders(String userId) async {
    try {
      final response = await client.get('/users/$userId/orders');
      
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return data.map((json) => OrderModel.fromJson(json)).toList();
      } else {
        throw ServerException('Failed to get user orders: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw ServerException(e.message ?? 'Unknown error');
    }
  }
  
  @override
  Future<void> createOrder(OrderModel order) async {
    try {
      final response = await client.post(
        '/orders',
        data: order.toJson(),
      );
      
      if (response.statusCode != 201) {
        throw ServerException('Failed to create order: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw ServerException(e.message ?? 'Unknown error');
    }
  }
}

// lib/features/orders/data/datasources/order_local_data_source.dart

class OrderLocalDataSourceImpl implements OrderLocalDataSource {
  final Database database;
  
  OrderLocalDataSourceImpl(this.database);
  
  @override
  Future<OrderModel?> getOrder(String id) async {
    final result = await database.query(
      'orders',
      where: 'id = ?',
      whereArgs: [id],
    );
    
    if (result.isEmpty) return null;
    return OrderModel.fromDatabase(result.first);
  }
  
  @override
  Future<void> saveOrder(OrderModel order) async {
    await database.insert(
      'orders',
      order.toDatabaseMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }
  
  @override
  Future<List<OrderModel>> getUserOrders(String userId) async {
    final result = await database.query(
      'orders',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
    );
    
    return result.map((map) => OrderModel.fromDatabase(map)).toList();
  }
  
  @override
  Future<void> markForSync(String orderId) async {
    await database.update(
      'orders',
      {'sync_needed': 1},
      where: 'id = ?',
      whereArgs: [orderId],
    );
  }
}
```

### 4. 表现层（Presentation Layer）- 用户界面的呈现

#### BLoC模式集成

```dart
// lib/features/orders/presentation/bloc/order_bloc.dart

class OrderBloc extends Bloc<OrderEvent, OrderState> {
  final CreateOrderUseCase createOrderUseCase;
  final GetOrderDetailsUseCase getOrderDetailsUseCase;
  final GetUserOrdersUseCase getUserOrdersUseCase;
  final CancelOrderUseCase cancelOrderUseCase;
  
  OrderBloc({
    required this.createOrderUseCase,
    required this.getOrderDetailsUseCase,
    required this.getUserOrdersUseCase,
    required this.cancelOrderUseCase,
  }) : super(const OrderState.initial()) {
    on<CreateOrderEvent>(_onCreateOrder);
    on<GetOrderDetailsEvent>(_onGetOrderDetails);
    on<GetUserOrdersEvent>(_onGetUserOrders);
    on<CancelOrderEvent>(_onCancelOrder);
  }
  
  Future<void> _onCreateOrder(
    CreateOrderEvent event,
    Emitter<OrderState> emit,
  ) async {
    emit(const OrderState.loading());
    
    try {
      final command = CreateOrderCommand(
        userId: event.userId,
        items: event.items,
        couponCode: event.couponCode,
      );
      
      final order = await createOrderUseCase.execute(command);
      emit(OrderState.loaded(order));
    } catch (e) {
      emit(OrderState.error(e.toString()));
    }
  }
  
  Future<void> _onGetUserOrders(
    GetUserOrdersEvent event,
    Emitter<OrderState> emit,
  ) async {
    emit(const OrderState.loading());
    
    try {
      final orders = await getUserOrdersUseCase.execute(event.userId);
      emit(OrderState.ordersLoaded(orders));
    } catch (e) {
      emit(OrderState.error(e.toString()));
    }
  }
}

// lib/features/orders/presentation/pages/order_list_page.dart

class OrderListPage extends StatelessWidget {
  const OrderListPage({Key? key}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<OrderBloc>()
        ..add(const GetUserOrdersEvent()),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('我的订单'),
        ),
        body: BlocBuilder<OrderBloc, OrderState>(
          builder: (context, state) {
            return state.when(
              initial: () => const SizedBox(),
              loading: () => const Center(child: CircularProgressIndicator()),
              loaded: (order) => OrderDetailsView(order: order),
              ordersLoaded: (orders) => OrdersListView(orders: orders),
              error: (message) => ErrorView(message: message),
            );
          },
        ),
      ),
    );
  }
}
```

---

## 第三章：依赖注入与模块化解耦

### 🔄 依赖注入架构

#### 使用GetIt + Injectable实现解耦

```dart
// lib/injection_container.dart

final getIt = GetIt.instance;

@InjectableInit(
  initializerName: 'init',
  preferRelativeImports: true,
  asExtension: false,
)
void configureDependencies(String environment) => getIt.init(environment: environment);

// lib/shared/infrastructure/di/injection_modules.dart

@module
abstract class DatabaseModule {
  @singleton
  @preResolve
  Future<Database> get database async {
    final dbPath = await getDatabasesPath();
    return openDatabase(
      join(dbPath, 'app_database.db'),
      version: 1,
      onCreate: (db, version) async {
        await db.execute('''
          CREATE TABLE orders (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            items TEXT NOT NULL,
            total_amount REAL NOT NULL,
            status TEXT NOT NULL,
            created_at TEXT NOT NULL,
            paid_at TEXT,
            sync_needed INTEGER DEFAULT 0
          )
        ''');
      },
    );
  }
}

@module
abstract class NetworkModule {
  @singleton
  Dio get dio {
    final dio = Dio();
    dio.options.baseUrl = 'https://api.example.com';
    dio.interceptors.addAll([
      AuthInterceptor(getIt<AuthLocalDataSource>()),
      LoggerInterceptor(),
    ]);
    return dio;
  }
}

@module
abstract class RepositoryModule {
  @singleton
  OrderRepository orderRepository(OrderRepositoryImpl impl) => impl;
  
  @singleton
  UserRepository userRepository(UserRepositoryImpl impl) => impl;
  
  @singleton
  ProductRepository productRepository(ProductRepositoryImpl impl) => impl;
}

@module
abstract class UseCaseModule {
  @singleton
  CreateOrderUseCase createOrderUseCase(
    OrderRepository orderRepository,
    UserRepository userRepository,
    ProductRepository productRepository,
    OrderPricingService pricingService,
    EventBus eventBus,
  ) => CreateOrderUseCase(
    orderRepository: orderRepository,
    userRepository: userRepository,
    productRepository: productRepository,
    pricingService: pricingService,
    eventBus: eventBus,
  );
  
  @singleton
  GetOrderDetailsUseCase getOrderDetailsUseCase(OrderRepository repository) =>
      GetOrderDetailsUseCase(repository);
  
  @singleton
  GetUserOrdersUseCase getUserOrdersUseCase(OrderRepository repository) =>
      GetUserOrdersUseCase(repository);
}

@module
abstract class ServiceModule {
  @singleton
  OrderPricingService orderPricingService(
    ExchangeRateService exchangeRateService,
    TaxService taxService,
    DiscountService discountService,
  ) => OrderPricingService(
    exchangeRateService: exchangeRateService,
    taxService: taxService,
    discountService: discountService,
  );
  
  @singleton
  EventBus eventBus() => EventBus();
}
```

### 🏗️ 模块化解耦策略

#### 按功能模块组织代码

```
lib/
├── core/
│   ├── domain/
│   │   ├── entities/
│   │   ├── value_objects/
│   │   └── failures.dart
│   ├── infrastructure/
│   │   ├── network/
│   │   ├── database/
│   │   └── services/
│   └── presentation/
│       ├── shared_widgets/
│       └── theme/
├── features/
│   ├── auth/
│   │   ├── domain/
│   │   │   ├── entities/
│   │   │   ├── repositories/
│   │   │   └── use_cases/
│   │   ├── data/
│   │   │   ├── datasources/
│   │   │   ├── models/
│   │   │   └── repositories/
│   │   └── presentation/
│   │       ├── bloc/
│   │       ├── pages/
│   │       └── widgets/
│   ├── orders/
│   │   ├── domain/
│   │   ├── data/
│   │   └── presentation/
│   └── products/
│       ├── domain/
│       ├── data/
│       └── presentation/
└── injection_container.dart
```

#### 跨模块交互

```dart
// lib/features/orders/domain/events/order_events.dart

class OrderCreatedEvent {
  final Order order;
  
  const OrderCreatedEvent(this.order);
}

// lib/features/notifications/application/services/notification_service.dart

class NotificationService {
  final EventBus eventBus;
  final PushNotificationService pushService;
  
  NotificationService({
    required this.eventBus,
    required this.pushService,
  }) {
    // 订阅订单创建事件
    eventBus.on<OrderCreatedEvent>().listen(_onOrderCreated);
  }
  
  void _onOrderCreated(OrderCreatedEvent event) {
    // 发送订单确认通知
    pushService.sendNotification(
      userId: event.order.userId,
      title: '订单创建成功',
      body: '订单号：${event.order.id.value}',
    );
  }
}

// lib/features/analytics/application/services/analytics_service.dart

class AnalyticsService {
  final EventBus eventBus;
  final AnalyticsRepository repository;
  
  AnalyticsService({
    required this.eventBus,
    required this.repository,
  }) {
    eventBus.on<OrderCreatedEvent>().listen(_trackOrderCreation);
  }
  
  void _trackOrderCreation(OrderCreatedEvent event) {
    repository.trackEvent('order_created', {
      'order_id': event.order.id.value,
      'user_id': event.order.userId.value,
      'item_count': event.order.items.length,
      'total_amount': event.order.totalAmount.amount,
    });
  }
}
```

---

## 第四章：测试策略 - 保证架构质量

### 🧪 分层测试策略

#### 1. 单元测试 - 测试业务逻辑

```dart
// test/features/orders/domain/entities/order_test.dart

void main() {
  group('Order Entity', () {
    late Order order;
    late Product product;
    
    setUp(() {
      product = Product(
        id: ProductId('1'),
        name: 'iPhone 15',
        price: Money(5999),
      );
      
      order = Order(
        id: OrderId('order-1'),
        userId: UserId('user-1'),
        items: [],
        totalAmount: Money(0),
        createdAt: DateTime.now(),
      );
    });
    
    test('should add item to order', () {
      // Act
      final updatedOrder = order.addItem(product, 2);
      
      // Assert
      expect(updatedOrder.items.length, 1);
      expect(updatedOrder.items[0].quantity, 2);
      expect(updatedOrder.totalAmount.amount, 11998);
    });
    
    test('should cancel pending order', () {
      // Act
      final cancelledOrder = order.cancel();
      
      // Assert
      expect(cancelledOrder.status, OrderStatus.cancelled);
    });
    
    test('should not cancel shipped order', () {
      // Arrange
      final shippedOrder = order.copyWith(status: OrderStatus.shipped);
      
      // Act & Assert
      expect(() => shippedOrder.cancel(), throwsException);
    });
  });
}

// test/features/orders/application/use_cases/create_order_use_case_test.dart

class MockOrderRepository extends Mock implements OrderRepository {}
class MockUserRepository extends Mock implements UserRepository {}
class MockProductRepository extends Mock implements ProductRepository {}

void main() {
  late CreateOrderUseCase useCase;
  late MockOrderRepository mockOrderRepository;
  late MockUserRepository mockUserRepository;
  late MockProductRepository mockProductRepository;
  
  setUp(() {
    mockOrderRepository = MockOrderRepository();
    mockUserRepository = MockUserRepository();
    mockProductRepository = MockProductRepository();
    
    useCase = CreateOrderUseCase(
      orderRepository: mockOrderRepository,
      userRepository: mockUserRepository,
      productRepository: mockProductRepository,
      pricingService: MockOrderPricingService(),
      eventBus: MockEventBus(),
    );
  });
  
  group('execute', () {
    test('should create order successfully', () async {
      // Arrange
      final user = User(id: UserId('user-1'), name: '张三');
      final product = Product(
        id: ProductId('1'),
        name: 'iPhone 15',
        price: Money(5999),
        stock: 10,
      );
      
      when(() => mockUserRepository.findById(any()))
          .thenAnswer((_) async => user);
      when(() => mockProductRepository.findById(any()))
          .thenAnswer((_) async => product);
      when(() => mockOrderRepository.save(any()))
          .thenAnswer((_) async => {});
      
      final command = CreateOrderCommand(
        userId: UserId('user-1'),
        items: [OrderItemCommand(productId: ProductId('1'), quantity: 1)],
      );
      
      // Act
      final order = await useCase.execute(command);
      
      // Assert
      expect(order.userId, UserId('user-1'));
      expect(order.items.length, 1);
      expect(order.totalAmount.amount, 5999);
      
      verify(() => mockOrderRepository.save(any())).called(1);
    });
    
    test('should throw when user not found', () async {
      // Arrange
      when(() => mockUserRepository.findById(any()))
          .thenAnswer((_) async => null);
      
      final command = CreateOrderCommand(
        userId: UserId('non-existent'),
        items: [],
      );
      
      // Act & Assert
      expect(() => useCase.execute(command), throwsA(isA<UserNotFoundException>()));
    });
  });
}
```

#### 2. 集成测试 - 测试数据流

```dart
// test/features/orders/data/repositories/order_repository_impl_test.dart

void main() {
  late OrderRepositoryImpl repository;
  late Database database;
  late Dio dio;
  late NetworkInfo networkInfo;
  
  setUp(() async {
    // 使用内存数据库进行测试
    database = await openDatabase(inMemoryDatabasePath);
    
    // 创建表
    await database.execute('''
      CREATE TABLE orders (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        items TEXT NOT NULL,
        total_amount REAL NOT NULL,
        status TEXT NOT NULL,
        created_at TEXT NOT NULL,
        paid_at TEXT
      )
    ''');
    
    dio = Dio();
    networkInfo = MockNetworkInfo();
    
    repository = OrderRepositoryImpl(
      remoteDataSource: OrderRemoteDataSourceImpl(dio: dio),
      localDataSource: OrderLocalDataSourceImpl(database),
      networkInfo: networkInfo,
    );
  });
  
  tearDown(() async {
    await database.close();
  });
  
  group('OrderRepositoryImpl', () {
    test('should save and retrieve order', () async {
      // Arrange
      final order = Order(
        id: OrderId('test-order-1'),
        userId: UserId('user-1'),
        items: [
          OrderItem(
            productId: ProductId('1'),
            name: 'Test Product',
            price: Money(100),
            quantity: 2,
          ),
        ],
        totalAmount: Money(200),
        createdAt: DateTime.now(),
      );
      
      // Act
      await repository.save(order);
      final retrievedOrder = await repository.findById(order.id);
      
      // Assert
      expect(retrievedOrder, isNotNull);
      expect(retrievedOrder!.id, order.id);
      expect(retrievedOrder.items.length, 1);
      expect(retrievedOrder.totalAmount.amount, 200);
    });
  });
}
```

#### 3. 组件测试 - 测试UI与业务逻辑集成

```dart
// test/features/orders/presentation/bloc/order_bloc_test.dart

void main() {
  late OrderBloc bloc;
  late MockCreateOrderUseCase mockCreateOrderUseCase;
  late MockGetUserOrdersUseCase mockGetUserOrdersUseCase;
  
  setUp(() {
    mockCreateOrderUseCase = MockCreateOrderUseCase();
    mockGetUserOrdersUseCase = MockGetUserOrdersUseCase();
    
    bloc = OrderBloc(
      createOrderUseCase: mockCreateOrderUseCase,
      getOrderDetailsUseCase: MockGetOrderDetailsUseCase(),
      getUserOrdersUseCase: mockGetUserOrdersUseCase,
      cancelOrderUseCase: MockCancelOrderUseCase(),
    );
  });
  
  blocTest<OrderBloc, OrderState>(
    'should emit loading and loaded states when creating order',
    build: () {
      when(() => mockCreateOrderUseCase.execute(any()))
          .thenAnswer((_) async => Order(
            id: OrderId('order-1'),
            userId: UserId('user-1'),
            items: [],
            totalAmount: Money(100),
            createdAt: DateTime.now(),
          ));
      
      return bloc;
    },
    act: (bloc) => bloc.add(CreateOrderEvent(
      userId: UserId('user-1'),
      items: [OrderItemCommand(productId: ProductId('1'), quantity: 1)],
    )),
    expect: () => [
      const OrderState.loading(),
      isA<OrderState>().having(
        (state) => state.maybeMap(
          loaded: (loaded) => loaded.order.id.value,
          orElse: () => null,
        ),
        'order id',
        'order-1',
      ),
    ],
  );
}
```

---

## 第五章：高级模式与最佳实践

### 🎯 事件驱动架构

#### 领域事件实现

```dart
// lib/shared/domain/events/domain_event.dart

abstract class DomainEvent {
  DateTime get occurredOn;
}

// lib/features/orders/domain/events/order_events.dart

class OrderCreatedEvent extends DomainEvent {
  final Order order;
  
  @override
  final DateTime occurredOn;
  
  OrderCreatedEvent(this.order) : occurredOn = DateTime.now();
}

class OrderStatusChangedEvent extends DomainEvent {
  final OrderId orderId;
  final OrderStatus oldStatus;
  final OrderStatus newStatus;
  
  @override
  final DateTime occurredOn;
  
  OrderStatusChangedEvent({
    required this.orderId,
    required this.oldStatus,
    required this.newStatus,
  }) : occurredOn = DateTime.now();
}

// lib/shared/infrastructure/services/event_bus.dart

class EventBus {
  final StreamController<DomainEvent> _controller = StreamController<DomainEvent>.broadcast();
  
  Stream<T> on<T extends DomainEvent>() {
    return _controller.stream.where((event) => event is T).cast<T>();
  }
  
  void publish(DomainEvent event) {
    _controller.add(event);
  }
  
  void dispose() {
    _controller.close();
  }
}

// lib/features/notifications/application/services/notification_service.dart

class NotificationService {
  final EventBus eventBus;
  final PushNotificationService pushService;
  final EmailService emailService;
  
  NotificationService({
    required this.eventBus,
    required this.pushService,
    required this.emailService,
  }) {
    _setupEventListeners();
  }
  
  void _setupEventListeners() {
    eventBus.on<OrderCreatedEvent>().listen(_handleOrderCreated);
    eventBus.on<OrderStatusChangedEvent>().listen(_handleOrderStatusChanged);
  }
  
  void _handleOrderCreated(OrderCreatedEvent event) {
    // 发送订单确认邮件
    emailService.sendOrderConfirmation(
      userId: event.order.userId,
      orderId: event.order.id,
      items: event.order.items,
    );
    
    // 推送订单创建通知
    pushService.sendNotification(
      userId: event.order.userId,
      title: '订单创建成功',
      body: '订单号：${event.order.id.value}',
    );
  }
  
  void _handleOrderStatusChanged(OrderStatusChangedEvent event) {
    // 根据状态变化发送相应通知
    switch (event.newStatus) {
      case OrderStatus.shipped:
        pushService.sendNotification(
          userId: UserId(''), // 需要从订单ID获取用户ID
          title: '订单已发货',
          body: '您的订单已发货，订单号：${event.orderId.value}',
        );
        break;
      case OrderStatus.delivered:
        pushService.sendNotification(
          userId: UserId(''), // 需要从订单ID获取用户ID
          title: '订单已送达',
          body: '您的订单已送达，订单号：${event.orderId.value}',
        );
        break;
      default:
        break;
    }
  }
}
```

### 🏗️ CQRS模式（命令查询职责分离）

```dart
// lib/features/orders/application/commands/create_order_command.dart

class CreateOrderCommand {
  final UserId userId;
  final List<OrderItemCommand> items;
  final String? couponCode;
  final Address? shippingAddress;
  
  const CreateOrderCommand({
    required this.userId,
    required this.items,
    this.couponCode,
    this.shippingAddress,
  });
}

// lib/features/orders/application/commands/create_order_command_handler.dart

class CreateOrderCommandHandler {
  final OrderRepository orderRepository;
  final UserRepository userRepository;
  final ProductRepository productRepository;
  final EventBus eventBus;
  
  CreateOrderCommandHandler({
    required this.orderRepository,
    required this.userRepository,
    required this.productRepository,
    required this.eventBus,
  });
  
  Future<OrderId> handle(CreateOrderCommand command) async {
    // 验证用户
    final user = await userRepository.findById(command.userId);
    if (user == null) {
      throw UserNotFoundException();
    }
    
    // 验证商品
    final items = <OrderItem>[];
    for (final itemCommand in command.items) {
      final product = await productRepository.findById(itemCommand.productId);
      if (product == null) {
        throw ProductNotFoundException(itemCommand.productId);
      }
      
      items.add(OrderItem(
        productId: product.id,
        name: product.name,
        price: product.price,
        quantity: itemCommand.quantity,
      ));
    }
    
    // 创建订单
    final orderId = OrderId(UniqueId.generate());
    final order = Order(
      id: orderId,
      userId: user.id,
      items: items,
      totalAmount: _calculateTotal(items),
      createdAt: DateTime.now(),
    );
    
    // 保存订单
    await orderRepository.save(order);
    
    // 发布事件
    await eventBus.publish(OrderCreatedEvent(order));
    
    return orderId;
  }
  
  Money _calculateTotal(List<OrderItem> items) {
    return items.fold(
      Money(0),
      (total, item) => total + (item.price * item.quantity),
    );
  }
}

// lib/features/orders/application/queries/get_user_orders_query.dart

class GetUserOrdersQuery {
  final UserId userId;
  final int page;
  final int pageSize;
  final OrderStatus? status;
  
  const GetUserOrdersQuery({
    required this.userId,
    this.page = 1,
    this.pageSize = 20,
    this.status,
  });
}

// lib/features/orders/application/queries/get_user_orders_query_handler.dart

class GetUserOrdersQueryHandler {
  final OrderRepository orderRepository;
  
  GetUserOrdersQueryHandler(this.orderRepository);
  
  Future<PaginatedResult<OrderDto>> handle(GetUserOrdersQuery query) async {
    var orders = await orderRepository.findByUserId(query.userId);
    
    if (query.status != null) {
      orders = orders.where((order) => order.status == query.status).toList();
    }
    
    // 分页
    final startIndex = (query.page - 1) * query.pageSize;
    final endIndex = startIndex + query.pageSize;
    final paginatedOrders = orders.sublist(
      startIndex.clamp(0, orders.length),
      endIndex.clamp(0, orders.length),
    );
    
    final orderDtos = paginatedOrders.map((order) => OrderDto.fromDomain(order)).toList();
    
    return PaginatedResult(
      items: orderDtos,
      totalCount: orders.length,
      page: query.page,
      pageSize: query.pageSize,
    );
  }
}
```

### 🔍 审计与日志

```dart
// lib/shared/domain/entities/audit_log.dart

class AuditLog extends Entity {
  final AuditLogId id;
  final String entityType;
  final String entityId;
  final String action;
  final String userId;
  final Map<String, dynamic> changes;
  final DateTime timestamp;
  
  const AuditLog({
    required this.id,
    required this.entityType,
    required this.entityId,
    required this.action,
    required this.userId,
    required this.changes,
    required this.timestamp,
  });
}

// lib/shared/infrastructure/services/audit_service.dart

class AuditService {
  final AuditLogRepository repository;
  final Clock clock;
  
  AuditService({
    required this.repository,
    required this.clock,
  });
  
  Future<void> logAction({
    required String entityType,
    required String entityId,
    required String action,
    required String userId,
    required Map<String, dynamic> changes,
  }) async {
    final auditLog = AuditLog(
      id: AuditLogId(UniqueId.generate()),
      entityType: entityType,
      entityId: entityId,
      action: action,
      userId: userId,
      changes: changes,
      timestamp: clock.now(),
    );
    
    await repository.save(auditLog);
  }
}

// 在领域实体中集成审计
class Order extends Entity {
  // ... 其他属性
  
  Order cancel(String userId) {
    if (status == OrderStatus.shipped) {
      throw Exception('已发货的订单不能取消');
    }
    
    final oldStatus = status;
    final newOrder = copyWith(status: OrderStatus.cancelled);
    
    // 记录审计日志
    auditService.logAction(
      entityType: 'Order',
      entityId: id.value,
      action: 'CANCEL',
      userId: userId,
      changes: {
        'status': {'old': oldStatus.name, 'new': newOrder.status.name},
      },
    );
    
    return newOrder;
  }
}
```

---

## 第六章：性能优化与扩展

### ⚡ 性能优化策略

#### 1. 缓存策略

```dart
// lib/shared/infrastructure/cache/cache_manager.dart

abstract class CacheManager<T> {
  Future<T?> get(String key);
  Future<void> set(String key, T value, {Duration? ttl});
  Future<void> invalidate(String key);
  Future<void> invalidateAll();
}

class InMemoryCacheManager<T> implements CacheManager<T> {
  final Map<String, _CacheEntry<T>> _cache = {};
  final Clock _clock;
  
  InMemoryCacheManager(this._clock);
  
  @override
  Future<T?> get(String key) async {
    final entry = _cache[key];
    if (entry == null) return null;
    
    if (entry.hasExpired(_clock.now())) {
      _cache.remove(key);
      return null;
    }
    
    return entry.value;
  }
  
  @override
  Future<void> set(String key, T value, {Duration? ttl}) async {
    final expiry = ttl != null ? _clock.now().add(ttl) : null;
    _cache[key] = _CacheEntry(value, expiry);
  }
  
  @override
  Future<void> invalidate(String key) async {
    _cache.remove(key);
  }
  
  @override
  Future<void> invalidateAll() async {
    _cache.clear();
  }
}

class _CacheEntry<T> {
  final T value;
  final DateTime? expiry;
  
  _CacheEntry(this.value, this.expiry);
  
  bool hasExpired(DateTime now) {
    return expiry != null && now.isAfter(expiry!);
  }
}

// 在仓库中使用缓存
class CachedOrderRepository implements OrderRepository {
  final OrderRepository _repository;
  final CacheManager<Order> _cache;
  
  CachedOrderRepository(this._repository, this._cache);
  
  @override
  Future<Order?> findById(OrderId id) async {
    final cacheKey = 'order_${id.value}';
    
    // 尝试从缓存获取
    final cachedOrder = await _cache.get(cacheKey);
    if (cachedOrder != null) return cachedOrder;
    
    // 从真实仓库获取
    final order = await _repository.findById(id);
    if (order != null) {
      await _cache.set(cacheKey, order, ttl: Duration(minutes: 5));
    }
    
    return order;
  }
  
  @override
  Future<void> save(Order order) async {
    await _repository.save(order);
    await _cache.invalidate('order_${order.id.value}');
  }
}
```

#### 2. 数据库优化

```dart
// lib/shared/infrastructure/database/query_optimizer.dart

class QueryOptimizer {
  static const _batchSize = 100;
  
  // 批量操作优化
  static Future<void> batchInsert<T>(
    Database db,
    String table,
    List<T> items,
    Map<String, dynamic> Function(T) toMap,
  ) async {
    await db.transaction((txn) async {
      for (var i = 0; i < items.length; i += _batchSize) {
        final batch = items.skip(i).take(_batchSize);
        final batchInsert = txn.batch();
        
        for (final item in batch) {
          batchInsert.insert(table, toMap(item));
        }
        
        await batchInsert.commit(noResult: true);
      }
    });
  }
  
  // 索引优化
  static Future<void> createIndexes(Database db) async {
    await db.execute('CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at DESC)');
  }
  
  // 分页查询优化
  static Future<List<Map<String, dynamic>>> paginatedQuery(
    Database db,
    String table, {
    required int page,
    required int pageSize,
    String? where,
    List<Object?>? whereArgs,
    String? orderBy,
  }) async {
    final offset = (page - 1) * pageSize;
    
    return await db.query(
      table,
      where: where,
      whereArgs: whereArgs,
      orderBy: orderBy,
      limit: pageSize,
      offset: offset,
    );
  }
}
```

#### 3. 网络优化

```dart
// lib/shared/infrastructure/network/network_optimizer.dart

class NetworkOptimizer {
  final Dio dio;
  final CacheManager<String> cacheManager;
  
  NetworkOptimizer(this.dio, this.cacheManager);
  
  // 请求去重
  final Map<String, Completer<Response>> _pendingRequests = {};
  
  Future<Response<T>> optimizedGet<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    bool useCache = true,
    Duration? cacheDuration,
  }) async {
    final cacheKey = '$path${queryParameters?.toString() ?? ''}';
    
    // 检查是否有进行中的相同请求
    if (_pendingRequests.containsKey(cacheKey)) {
      return await _pendingRequests[cacheKey]!.future as Response<T>;
    }
    
    // 检查缓存
    if (useCache) {
      final cachedResponse = await cacheManager.get(cacheKey);
      if (cachedResponse != null) {
        return Response(
          requestOptions: RequestOptions(path: path),
          data: jsonDecode(cachedResponse),
        ) as Response<T>;
      }
    }
    
    // 创建新的请求
    final completer = Completer<Response>();
    _pendingRequests[cacheKey] = completer;
    
    try {
      final response = await dio.get<T>(
        path,
        queryParameters: queryParameters,
      );
      
      if (useCache && response.statusCode == 200) {
        await cacheManager.set(
          cacheKey,
          jsonEncode(response.data),
          ttl: cacheDuration ?? Duration(minutes: 5),
        );
      }
      
      completer.complete(response);
      return response;
    } catch (e) {
      completer.completeError(e);
      rethrow;
    } finally {
      _pendingRequests.remove(cacheKey);
    }
  }
}
```

### 🚀 扩展性设计

#### 1. 插件式架构

```dart
// lib/shared/domain/plugins/plugin_interface.dart

abstract class Plugin<T> {
  String get name;
  String get version;
  
  Future<void> initialize();
  Future<void> dispose();
}

abstract class PaymentPlugin extends Plugin<PaymentPlugin> {
  Future<PaymentResult> processPayment(PaymentRequest request);
  Future<PaymentStatus> checkPaymentStatus(String paymentId);
}

abstract class ShippingPlugin extends Plugin<ShippingPlugin> {
  Future<ShippingRate> calculateRate(ShippingRequest request);
  Future<TrackingInfo> getTrackingInfo(String trackingNumber);
}

// lib/features/orders/infrastructure/plugins/payment_plugins.dart

class AlipayPlugin implements PaymentPlugin {
  @override
  String get name => 'Alipay';
  @override
  String get version => '1.0.0';
  
  @override
  Future<void> initialize() async {
    // 初始化支付宝SDK
  }
  
  @override
  Future<void> dispose() async {
    // 清理资源
  }
  
  @override
  Future<PaymentResult> processPayment(PaymentRequest request) async {
    // 实现支付宝支付逻辑
    return PaymentResult.success('alipay_${UniqueId.generate()}');
  }
  
  @override
  Future<PaymentStatus> checkPaymentStatus(String paymentId) async {
    // 查询支付宝支付状态
    return PaymentStatus.completed;
  }
}

class WeChatPayPlugin implements PaymentPlugin {
  @override
  String get name => 'WeChatPay';
  @override
  String get version => '1.0.0';
  
  @override
  Future<PaymentResult> processPayment(PaymentRequest request) async {
    // 实现微信支付逻辑
    return PaymentResult.success('wxpay_${UniqueId.generate()}');
  }
  
  @override
  Future<PaymentStatus> checkPaymentStatus(String paymentId) async {
    // 查询微信支付状态
    return PaymentStatus.completed;
  }
  
  @override
  Future<void> initialize() async {}
  @override
  Future<void> dispose() async {}
}

// lib/features/orders/application/services/plugin_manager.dart

class PluginManager {
  final Map<String, PaymentPlugin> _paymentPlugins = {};
  final Map<String, ShippingPlugin> _shippingPlugins = {};
  
  void registerPaymentPlugin(PaymentPlugin plugin) {
    _paymentPlugins[plugin.name] = plugin;
  }
  
  void registerShippingPlugin(ShippingPlugin plugin) {
    _shippingPlugins[plugin.name] = plugin;
  }
  
  PaymentPlugin? getPaymentPlugin(String name) => _paymentPlugins[name];
  ShippingPlugin? getShippingPlugin(String name) => _shippingPlugins[name];
  
  List<String> get availablePaymentMethods => _paymentPlugins.keys.toList();
  List<String> get availableShippingMethods => _shippingPlugins.keys.toList();
}
```

#### 2. 配置驱动的架构

```dart
// lib/shared/domain/config/app_config.dart

class AppConfig {
  final String apiBaseUrl;
  final int cacheExpirationMinutes;
  final bool enableDebugLogging;
  final PaymentConfig paymentConfig;
  final ShippingConfig shippingConfig;
  
  const AppConfig({
    required this.apiBaseUrl,
    required this.cacheExpirationMinutes,
    required this.enableDebugLogging,
    required this.paymentConfig,
    required this.shippingConfig,
  });
  
  factory AppConfig.fromJson(Map<String, dynamic> json) {
    return AppConfig(
      apiBaseUrl: json['apiBaseUrl'],
      cacheExpirationMinutes: json['cacheExpirationMinutes'],
      enableDebugLogging: json['enableDebugLogging'],
      paymentConfig: PaymentConfig.fromJson(json['payment']),
      shippingConfig: ShippingConfig.fromJson(json['shipping']),
    );
  }
}

class PaymentConfig {
  final String defaultProvider;
  final Map<String, dynamic> providerSettings;
  
  const PaymentConfig({
    required this.defaultProvider,
    required this.providerSettings,
  });
  
  factory PaymentConfig.fromJson(Map<String, dynamic> json) {
    return PaymentConfig(
      defaultProvider: json['defaultProvider'],
      providerSettings: Map<String, dynamic>.from(json['providerSettings']),
    );
  }
}

// lib/main.dart

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 加载配置
  final config = await loadAppConfig();
  
  // 配置依赖注入
  configureDependencies(config);
  
  runApp(MyApp(config: config));
}

Future<AppConfig> loadAppConfig() async {
  final configString = await rootBundle.loadString('assets/config/app_config.json');
  final configJson = jsonDecode(configString);
  return AppConfig.fromJson(configJson);
}
```

---

## 第七章：实战项目 - 完整电商系统

### 🛒 项目概览

#### 系统架构图

```
┌─────────────────────────────────────────────────────┐
│                用户界面层                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │
│  │   Flutter   │  │   WebApp    │  │   Admin     │  │
│  │    App      │  │   (React)   │  │   Panel     │  │
│  └─────────────┘  └─────────────┘  └─────────────┘  │
├─────────────────────────────────────────────────────┤
│                API网关层                              │
│  ┌─────────────────────────────────────────────────┐  │
│  │              Kong / Nginx                       │  │
│  │        认证 / 限流 / 负载均衡                    │  │
│  └─────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────┤
│                应用服务层                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │
│  │   订单服务   │  │   用户服务   │  │   商品服务   │  │
│  │   支付服务   │  │   物流服务   │  │   通知服务   │  │
│  └─────────────┘  └─────────────┘  └─────────────┘  │
├─────────────────────────────────────────────────────┤
│                领域层                                 │
│  ┌─────────────────────────────────────────────────┐  │
│  │              核心业务逻辑                        │  │
│  │     订单聚合 / 用户聚合 / 商品聚合               │  │
│  └─────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────┤
│                基础设施层                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │
│  │   PostgreSQL│  │    Redis    │  │ Elasticsearch│  │
│  │   RabbitMQ  │  │   MinIO     │  │   Prometheus│  │
│  └─────────────┘  └─────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────┘
```

#### 核心功能模块

```dart
// lib/features/orders/domain/entities/order_aggregate.dart

class OrderAggregate extends AggregateRoot {
  final Order order;
  final List<OrderEvent> events;
  
  OrderAggregate({
    required this.order,
    this.events = const [],
  });
  
  // 业务逻辑处理
  OrderAggregate addItem(Product product, int quantity) {
    final newOrder = order.addItem(product, quantity);
    final event = OrderItemAddedEvent(
      orderId: order.id,
      productId: product.id,
      quantity: quantity,
    );
    
    return OrderAggregate(
      order: newOrder,
      events: [...events, event],
    );
  }
  
  OrderAggregate applyCoupon(Coupon coupon) {
    // 验证优惠券
    if (!coupon.isValid()) {
      throw InvalidCouponException();
    }
    
    final discount = coupon.calculateDiscount(order.totalAmount);
    final newOrder = order.copyWith(
      totalAmount: order.totalAmount - discount,
      appliedCoupon: coupon,
    );
    
    final event = CouponAppliedEvent(
      orderId: order.id,
      couponId: coupon.id,
      discountAmount: discount,
    );
    
    return OrderAggregate(
      order: newOrder,
      events: [...events, event],
    );
  }
}

// lib/features/orders/application/services/order_workflow_service.dart

class OrderWorkflowService {
  final OrderRepository orderRepository;
  final PaymentService paymentService;
  final InventoryService inventoryService;
  final ShippingService shippingService;
  final NotificationService notificationService;
  
  OrderWorkflowService({
    required this.orderRepository,
    required this.paymentService,
    required this.inventoryService,
    required this.shippingService,
    required this.notificationService,
  });
  
  // 完整的订单处理流程
  Future<Order> processOrder(Order order) async {
    try {
      // 1. 库存检查
      await _checkInventory(order);
      
      // 2. 价格计算
      final finalPrice = await _calculateFinalPrice(order);
      
      // 3. 创建支付订单
      final paymentOrder = await paymentService.createPaymentOrder(
        orderId: order.id,
        amount: finalPrice,
      );
      
      // 4. 等待支付完成
      final paymentResult = await paymentService.waitForPayment(paymentOrder.id);
      
      if (paymentResult.status == PaymentStatus.success) {
        // 5. 扣减库存
        await inventoryService.deductStock(order.items);
        
        // 6. 更新订单状态
        final paidOrder = order.pay(paymentResult.paidAt);
        await orderRepository.save(paidOrder);
        
        // 7. 创建物流订单
        final shippingOrder = await shippingService.createShippingOrder(
          order: paidOrder,
          address: order.shippingAddress,
        );
        
        // 8. 发送通知
        await notificationService.sendOrderConfirmation(paidOrder);
        
        return paidOrder;
      } else {
        throw PaymentFailedException('支付失败: ${paymentResult.errorMessage}');
      }
    } catch (e) {
      // 错误处理
      await _handleOrderFailure(order, e);
      rethrow;
    }
  }
  
  Future<void> _checkInventory(Order order) async {
    for (final item in order.items) {
      final availableStock = await inventoryService.getStock(item.productId);
      if (availableStock < item.quantity) {
        throw InsufficientStockException(
          productId: item.productId,
          requested: item.quantity,
          available: availableStock,
        );
      }
    }
  }
  
  Future<Money> _calculateFinalPrice(Order order) async {
    var total = order.totalAmount;
    
    // 应用优惠券
    if (order.appliedCoupon != null) {
      final discount = order.appliedCoupon!.calculateDiscount(total);
      total = total - discount;
    }
    
    // 计算运费
    final shippingCost = await shippingService.calculateShippingCost(
      order: order,
      address: order.shippingAddress,
    );
    total = total + shippingCost;
    
    return total;
  }
  
  Future<void> _handleOrderFailure(Order order, dynamic error) async {
    // 记录错误日志
    await logger.error('Order processing failed', error: error);
    
    // 发送失败通知
    await notificationService.sendOrderFailureNotification(order, error);
    
    // 恢复库存（如果已扣减）
    if (error is! InsufficientStockException) {
      await inventoryService.restoreStock(order.items);
    }
  }
}
```

### 📊 监控与运维

```dart
// lib/shared/infrastructure/monitoring/app_metrics.dart

class AppMetrics {
  static final Counter orderCounter = Counter(
    name: 'orders_total',
    help: 'Total number of orders created',
    labelNames: ['status', 'payment_method'],
  );
  
  static final Histogram orderProcessingTime = Histogram(
    name: 'order_processing_seconds',
    help: 'Time spent processing orders',
    buckets: [0.1, 0.5, 1.0, 2.0, 5.0, 10.0],
  );
  
  static final Gauge activeUsers = Gauge(
    name: 'active_users',
    help: 'Number of active users',
  );
}

// lib/shared/infrastructure/monitoring/middleware.dart

class MonitoringMiddleware {
  final Logger logger;
  final MetricsCollector metrics;
  
  MonitoringMiddleware({
    required this.logger,
    required this.metrics,
  });
  
  Future<T> track<T>(
    String operation,
    Future<T> Function() action,
  ) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = await action();
      
      stopwatch.stop();
      metrics.recordSuccess(operation, stopwatch.elapsedMilliseconds);
      
      return result;
    } catch (e, stack) {
      stopwatch.stop();
      metrics.recordFailure(operation, stopwatch.elapsedMilliseconds);
      
      logger.error('Operation failed', error: e, stackTrace: stack);
      rethrow;
    }
  }
}
```

---

## 📚 学习路径总结

### 🎯 掌握Clean Architecture的五个阶段

#### 阶段1：理解核心概念（1-2周）
1. ✅ **实体与值对象**
   - 理解什么是领域模型
   - 掌握实体和值对象的区别
   - 学会设计不可变对象

2. ✅ **仓库模式**
   - 理解抽象与实现的分离
   - 掌握依赖倒置原则
   - 学会接口设计

#### 阶段2：架构实践（2-3周）
1. ✅ **用例设计**
   - 掌握单一职责原则
   - 学会用例的输入输出设计
   - 理解业务流程的抽象

2. ✅ **依赖注入**
   - 掌握GetIt + Injectable
   - 理解控制反转
   - 学会模块化配置

#### 阶段3：高级模式（2-3周）
1. ✅ **事件驱动**
   - 掌握领域事件
   - 理解CQRS模式
   - 学会事件溯源

2. ✅ **测试策略**
   - 掌握分层测试
   - 学会Mock和Stub
   - 理解测试金字塔

#### 阶段4：性能优化（1-2周）
1. ✅ **缓存策略**
   - 掌握多级缓存
   - 理解缓存失效策略
   - 学会监控缓存命中率

2. ✅ **数据库优化**
   - 掌握查询优化
   - 理解索引设计
   - 学会批量操作

#### 阶段5：实战项目（2-3周）
1. ✅ **完整电商系统**
   - 用户管理模块
   - 商品管理模块
   - 订单管理模块
   - 支付管理模块

### 🛠️ 实战建议

#### 每日练习计划

**第1周：基础架构搭建**
```dart
// 任务1：创建第一个实体
class User extends Entity {
  final UserId id;
  final String name;
  final Email email;
  
  const User({required this.id, required this.name, required this.email});
}

// 任务2：创建第一个仓库接口
abstract class UserRepository {
  Future<User?> findById(UserId id);
  Future<void> save(User user);
}

// 任务3：创建第一个用例
class CreateUserUseCase {
  final UserRepository repository;
  
  CreateUserUseCase(this.repository);
  
  Future<User> execute(CreateUserCommand command) async {
    final user = User(
      id: UserId(UniqueId.generate()),
      name: command.name,
      email: Email(command.email),
    );
    
    await repository.save(user);
    return user;
  }
}
```

**第2周：集成测试**
```dart
// 任务1：写单元测试
void main() {
  test('should create user successfully', () async {
    final repository = MockUserRepository();
    final useCase = CreateUserUseCase(repository);
    
    when(() => repository.save(any())).thenAnswer((_) async {});
    
    final user = await useCase.execute(CreateUserCommand(
      name: '张三',
      email: '<EMAIL>',
    ));
    
    expect(user.name, '张三');
    expect(user.email.value, '<EMAIL>');
  });
}
```

**第3周：复杂业务场景**
```dart
// 任务1：设计订单聚合
class OrderAggregate extends AggregateRoot {
  final Order order;
  final List<OrderItem> items;
  final PaymentInfo payment;
  
  // 复杂的业务逻辑
  OrderAggregate applyDiscount(Coupon coupon) {
    // 验证优惠券
    // 计算折扣
    // 更新总价
    // 记录事件
  }
}
```

### 📖 推荐阅读顺序

1. **《Clean Architecture》** - Robert C. Martin
2. **《领域驱动设计》** - Eric Evans
3. **《实现领域驱动设计》** - Vaughn Vernon
4. **《架构整洁之道》** - Robert C. Martin
5. **《微服务架构设计模式》** - Chris Richardson

### 🔧 开发工具推荐

```bash
# 代码分析
flutter analyze

# 测试运行
flutter test --coverage

# 性能分析
flutter run --profile

# 依赖检查
flutter pub deps

# 代码格式化
flutter format .
```

### 🎓 学习资源

1. **官方文档**：[Clean Architecture Flutter](https://resocoder.com/category/tutorials/flutter/clean-architecture/)
2. **实战项目**：[Flutter Clean Architecture Example](https://github.com/ResoCoder/flutter-tdd-clean-architecture-course)
3. **最佳实践**：[Flutter Architecture Samples](https://github.com/brianegan/flutter_architecture_samples)

记住：**Clean Architecture不是银弹，但它能让你的代码像城市一样易于理解和维护！** 通过系统学习和大量实践，你将成为真正的架构大师！