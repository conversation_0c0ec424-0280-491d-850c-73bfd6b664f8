import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

import '../models/auth_user_model.dart';

part 'auth_remote_data_source.g.dart';

@lazySingleton
@RestApi()
abstract class AuthRemoteDataSource {
  @factoryMethod
  factory AuthRemoteDataSource(Dio dio) = _AuthRemoteDataSource;

  @POST('/auth/login')
  Future<AuthUserModel> login(
    @Body() Map<String, dynamic> body,
  );

  @POST('/auth/register')
  Future<AuthUserModel> register(
    @Body() Map<String, dynamic> body,
  );

  @POST('/auth/refresh')
  Future<AuthUserModel> refreshToken(
    @Body() Map<String, dynamic> body,
  );

  @POST('/auth/forgot-password')
  Future<void> forgotPassword(
    @Body() Map<String, dynamic> body,
  );

  @GET('/auth/me')
  Future<AuthUserModel> getCurrentUser(
    @Header('Authorization') String token,
  );

  @POST('/auth/logout')
  Future<void> logout(
    @Header('Authorization') String token,
  );

  @POST('/auth/sms/login')
  Future<AuthUserModel> smsLogin(
    @Body() Map<String, dynamic> body,
  );

  @POST('/auth/sms/send-code')
  Future<void> sendSmsCode(
    @Body() Map<String, dynamic> body,
  );

  @POST('/auth/wechat/login')
  Future<AuthUserModel> wechatLogin();
}