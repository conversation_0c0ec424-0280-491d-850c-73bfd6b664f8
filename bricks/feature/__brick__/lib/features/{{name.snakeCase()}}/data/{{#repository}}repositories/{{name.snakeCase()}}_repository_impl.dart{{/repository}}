import 'package:injectable/injectable.dart';
import 'package:fpdart/fpdart.dart';

import '../../domain/repositories/{{name.snakeCase()}}_repository.dart';

@Injectable(as: {{name.pascalCase()}}Repository)
class {{name.pascalCase()}}RepositoryImpl implements {{name.pascalCase()}}Repository {
  @override
  Future<Either<Failure, {{name.pascalCase()}}Entity>> get{{name.pascalCase()}}() {
    // TODO: Implement repository logic
    throw UnimplementedError();
  }
}