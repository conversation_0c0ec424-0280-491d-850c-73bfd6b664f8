part of '{{name.snakeCase()}}_bloc.dart';

@freezed
class {{name.pascalCase()}}State with _${{name.pascalCase()}}State {
  const factory {{name.pascalCase()}}State.initial() = _Initial;
  const factory {{name.pascalCase()}}State.loading() = _Loading;
  const factory {{name.pascalCase()}}State.loaded({{entity.pascalCase()}} {{entity.camelCase()}}) = _Loaded;
  const factory {{name.pascalCase()}}State.listLoaded(List<{{entity.pascalCase()}}> {{entity.camelCase()}}s) = _ListLoaded;
  const factory {{name.pascalCase()}}State.error(String message) = _Error;
}