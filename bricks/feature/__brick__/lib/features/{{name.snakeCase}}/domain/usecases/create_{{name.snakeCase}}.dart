import 'package:fpdart/fpdart.dart';
import '../../../../../core/error/failure.dart';
import '../../../../../core/usecase/usecase.dart';
import '../../domain/entities/{{name.snakeCase}}.dart';
import '../../domain/repositories/{{name.snakeCase}}_repository.dart';

class Create{{name.pascalCase}}UseCase implements UseCase<{{name.pascalCase}}, Create{{name.pascalCase}}Params> {
  final {{name.pascalCase}}Repository repository;

  const Create{{name.pascalCase}}UseCase(this.repository);

  @override
  Future<Either<Failure, {{name.pascalCase}}>> call(Create{{name.pascalCase}}Params params) async {
    return await repository.create{{name.pascalCase}}(params.{{name.camelCase}});
  }
}

class Create{{name.pascalCase}}Params {
  final {{name.pascalCase}} {{name.camelCase}};

  Create{{name.pascalCase}}Params({required this.{{name.camelCase}});
}