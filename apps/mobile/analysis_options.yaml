# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.config.dart"
    - "**/*.gr.dart"
    - "**/*.mocks.dart"
  errors:
    # 临时忽略 JsonKey 注解错误，升级到 Freezed 3.x 后将自动解决
    invalid_annotation_target: ignore

linter:
  rules:
    # 废弃 API 检测
    - deprecated_member_use_from_same_package

    # Material Design 3 最佳实践
    - use_colored_box
    - use_decorated_box

    # 性能优化
    - avoid_unnecessary_containers
    - sized_box_for_whitespace
    - prefer_const_constructors
    - prefer_const_literals_to_create_immutables

    # Freezed 3.x 最佳实践
    - prefer_final_fields
    - avoid_redundant_argument_values

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options
