import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mocktail/mocktail.dart';
import 'package:ui_library/ui_library.dart';

import 'package:flutter_scaffold_mobile/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:flutter_scaffold_mobile/features/auth/presentation/pages/login_page.dart';

class MockAuthBloc extends Mock implements AuthBloc {
  final _completer = Completer<void>();

  @override
  Future<void> close() {
    if (!_completer.isCompleted) {
      _completer.complete();
    }
    return _completer.future;
  }
}

void main() {
  late MockAuthBloc mockAuthBloc;

  setUp(() {
    mockAuthBloc = MockAuthBloc();
    when(() => mockAuthBloc.state).thenReturn(const AuthState());
    when(() => mockAuthBloc.stream).thenAnswer((_) => Stream.value(const AuthState()));
  });

  Widget createWidgetUnderTest() {
    return MaterialApp(
      home: BlocProvider<AuthBloc>(
        create: (_) => mockAuthBloc,
        child: const LoginPage(),
      ),
    );
  }

  Future<void> pumpWidgetIgnoringOverflow(WidgetTester tester, Widget widget) async {
    // Store original error handler
    final originalOnError = FlutterError.onError;

    try {
      // Ignore overflow errors during testing
      FlutterError.onError = (FlutterErrorDetails details) {
        final exception = details.exception;
        final isOverflowError = exception is FlutterError &&
            exception.message.contains('overflowed by');

        if (!isOverflowError && originalOnError != null) {
          originalOnError(details);
        }
      };

      await tester.pumpWidget(widget);
      await tester.pumpAndSettle();
    } finally {
      // Always restore error handling
      FlutterError.onError = originalOnError;
    }
  }

  group('LoginPage Widget Tests', () {
    testWidgets('renders login form correctly', (tester) async {
      await pumpWidgetIgnoringOverflow(tester, createWidgetUnderTest());

      expect(find.text('欢迎回来'), findsOneWidget);
      expect(find.text('请使用手机号码登录'), findsOneWidget);
      expect(find.byType(AppTextField), findsNWidgets(2));
      expect(find.text('登录'), findsOneWidget);
    });

    testWidgets('displays validation errors when form is submitted empty', (tester) async {
      await pumpWidgetIgnoringOverflow(tester, createWidgetUnderTest());

      await tester.tap(find.text('登录'));
      await tester.pump();

      expect(find.text('请输入手机号码'), findsOneWidget);
      expect(find.text('请输入验证码'), findsOneWidget);
    });

    testWidgets('displays phone validation error for invalid phone', (tester) async {
      await pumpWidgetIgnoringOverflow(tester, createWidgetUnderTest());

      final phoneField = find.byType(AppTextField).first;
      await tester.enterText(phoneField, '123');
      await tester.tap(find.text('登录'));
      await tester.pump();

      expect(find.text('请输入正确的手机号码'), findsOneWidget);
    });

    testWidgets('shows send verification code button', (tester) async {
      await pumpWidgetIgnoringOverflow(tester, createWidgetUnderTest());

      expect(find.text('获取验证码'), findsOneWidget);
    });

    testWidgets('submits form with valid data', (tester) async {
      when(() => mockAuthBloc.state).thenReturn(
        const AuthState(status: AuthStatus.unauthenticated),
      );
      when(() => mockAuthBloc.stream).thenAnswer((_) => Stream.value(
        const AuthState(status: AuthStatus.unauthenticated),
      ));

      await pumpWidgetIgnoringOverflow(tester, createWidgetUnderTest());

      final phoneField = find.byType(AppTextField).first;
      final codeField = find.byType(AppTextField).last;

      await tester.enterText(phoneField, '13800138000');
      await tester.enterText(codeField, '123456');

      await tester.tap(find.text('登录'));
      await tester.pumpAndSettle();

      verify(() => mockAuthBloc.add(const AuthSmsLoginRequested(
        phone: '13800138000',
        code: '123456',
      ))).called(1);
    });
  });
}