name: flutter_scaffold

vars:
  author: "Flutter Scaffold Team"
  email: "<EMAIL>"

bricks:
  feature:
    path: bricks/feature
  model:
    path: bricks/model
  repository:
    path: bricks/repository
  usecase:
    path: bricks/usecase
  bloc:
    path: bricks/bloc
  page:
    path: bricks/page
  widget:
    path: bricks/widget
  adr:
    path: bricks/adr
  data_source:
    path: bricks/data_source
  api_client:
    path: bricks/api_client
  validator:
    path: bricks/validator
  test:
    path: bricks/test
  service:
    path: bricks/service