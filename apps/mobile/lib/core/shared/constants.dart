class AppConstants {
  static const String appName = 'Flutter Scaffold';
  
  // API
  static const String baseUrl = 'https://api.example.com';
  static const String apiVersion = 'v1';
  // Optimized timeout for mobile devices
  static const Duration connectTimeout = Duration(seconds: 10);
  static const Duration receiveTimeout = Duration(seconds: 15);
  static const Duration sendTimeout = Duration(seconds: 10);
  
  // Storage
  static const String userBox = 'user_box';
  static const String settingsBox = 'settings_box';
  
  // Routes
  static const String initialRoute = '/';
  static const String loginRoute = '/login';
  static const String homeRoute = '/home';
  static const String profileRoute = '/profile';
  
  // Keys
  static const String accessTokenKey = 'access_token';
  static const String authTokenKey = 'auth_token'; // Keep for backward compatibility
  static const String refreshTokenKey = 'refresh_token';
  static const String userIdKey = 'user_id';
}