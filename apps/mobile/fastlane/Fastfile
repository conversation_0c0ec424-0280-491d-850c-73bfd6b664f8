default_platform(:android)

platform :android do
  desc "Deploy a new version to the Google Play Store"
  lane :deploy do
    gradle(
      task: "bundle",
      build_type: "Release",
      flavor: "Production"
    )

    upload_to_play_store(
      track: "internal",
      aab: "../build/app/outputs/bundle/productionRelease/app-production-release.aab",
      skip_upload_metadata: true,
      skip_upload_changelogs: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  end

  desc "Deploy a new version to Firebase App Distribution"
  lane :firebase_deploy do
    gradle(
      task: "assemble",
      build_type: "Release",
      flavor: "Staging"
    )

    firebase_app_distribution(
      app: ENV["FIREBASE_APP_ID"],
      testers: "<EMAIL>",
      release_notes: "New build from CI/CD",
      apk_path: "../build/app/outputs/flutter-apk/app-staging-release.apk"
    )
  end
end

platform :ios do
  desc "Deploy a new version to TestFlight"
  lane :deploy do
    increment_build_number(xcodeproj: "Runner.xcodeproj")
    build_app(
      scheme: "Production",
      export_method: "app-store"
    )
    upload_to_testflight(
      skip_waiting_for_build_processing: true
    )
  end
end