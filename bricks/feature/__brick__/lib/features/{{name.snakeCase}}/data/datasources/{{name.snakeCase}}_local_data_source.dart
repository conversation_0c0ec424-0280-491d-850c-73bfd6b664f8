import '../../domain/entities/{{name.snakeCase}}.dart';

abstract interface class {{name.pascalCase}}LocalDataSource {
  Future<{{name.pascalCase}}> create{{name.pascalCase}}({{name.pascalCase}} {{name.camelCase}});
  
  Future<{{name.pascalCase}}> get{{name.pascalCase}}(String id);
  
  Future<List<{{name.pascalCase}}>> getAll{{name.pascalCase}}s();
  
  Future<{{name.pascalCase}}> update{{name.pascalCase}}({{name.pascalCase}} {{name.camelCase}});
  
  Future<void> delete{{name.pascalCase}}(String id);
}