import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/{{entity.snakeCase}}.dart';

part '{{name.snakeCase}}_event.freezed.dart';

@freezed
class {{name.pascalCase}}Event with _${{name.pascalCase}}Event {
  const factory {{name.pascalCase}}Event.load{{entity.pascalCase}}s() = Load{{entity.pascalCase}}s;
  
  const factory {{name.pascalCase}}Event.load{{entity.pascalCase}}(String id) = Load{{entity.pascalCase}};
  
  const factory {{name.pascalCase}}Event.create{{entity.pascalCase}}({{entity.pascalCase}} {{entity.camelCase}}) = Create{{entity.pascalCase}};
  
  const factory {{name.pascalCase}}Event.update{{entity.pascalCase}}({{entity.pascalCase}} {{entity.camelCase}}) = Update{{entity.pascalCase}};
  
  const factory {{name.pascalCase}}Event.delete{{entity.pascalCase}}(String id) = Delete{{entity.pascalCase}};
}