import 'package:flutter/material.dart';
import '../../domain/entities/{{name.snakeCase}}.dart';

class {{name.pascalCase}}Widget extends StatelessWidget {
  final {{name.pascalCase}} {{name.camelCase}};

  const {{name.pascalCase}}Widget({super.key, required this.{{name.camelCase}}});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        title: Text({{name.camelCase}}.name),
        subtitle: Text({{name.camelCase}}.id),
      ),
    );
  }
}