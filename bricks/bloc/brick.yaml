name: bloc
description: Creates a new BLoC with events, states, and bloc class
version: 0.1.0

vars:
  name:
    type: string
    description: BLoC name
    default: example
    prompt: What is the name of the BLoC?
  
  feature:
    type: string
    description: Feature name
    default: core
    prompt: What feature does this BLoC belong to?
  
  entity:
    type: string
    description: Entity name
    default: ""
    prompt: What entity does this BLoC manage?