import 'package:equatable/equatable.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/error/failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../repositories/auth_repository.dart';

class SendSmsCodeParams extends Equatable {
  final String phone;

  const SendSmsCodeParams({
    required this.phone,
  });

  @override
  List<Object> get props => [phone];
}

@injectable
class SendSmsCodeUseCase implements UseCase<void, SendSmsCodeParams> {
  final AuthRepository repository;

  const SendSmsCodeUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(SendSmsCodeParams params) async {
    return await repository.sendSmsCode(phone: params.phone);
  }
}