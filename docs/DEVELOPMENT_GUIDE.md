# Flutter Scaffold - 项目开发指南

## 📋 开发环境配置

### 前置要求
- Flutter 3.32.5+
- Dart 3.6.0+
- Git
- Make (可选)

### 快速开始
```bash
# 1. 克隆项目
git clone https://github.com/your-org/flutter-scaffold.git
cd flutter-scaffold

# 2. 环境设置
make setup

# 3. 依赖安装
melos bootstrap

# 4. 代码生成
make gen

# 5. 运行应用
make run-dev
```

## 🏗️ 项目结构详解

### 核心目录结构
```
flutter_scaffold/
├── apps/mobile/              # 主应用
│   ├── lib/
│   │   ├── core/            # 核心基础设施
│   │   ├── features/        # 业务功能
│   │   └── main_*.dart      # 环境入口
│   ├── test/                # 测试文件
│   └── fastlane/           # 自动化部署
├── packages/               # 共享包
│   ├── ui_library/         # UI 组件库
│   └── data_models/        # 共享数据模型
├── bricks/                 # 代码模板
├── docs/                   # 项目文档
└── melos.yaml            # Monorepo 配置
```

### Clean Architecture 实现

#### 表现层 (Presentation)
```dart
// lib/features/auth/presentation/pages/login_page.dart
class LoginPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocProvider<AuthBloc>(
      create: (context) => di.getIt<AuthBloc>()..add(AuthStarted()),
      child: LoginView(),
    );
  }
}
```

#### 领域层 (Domain)
```dart
// lib/features/auth/domain/entities/auth_user.dart
@freezed
class AuthUser with _$AuthUser {
  const factory AuthUser({
    required String id,
    required String email,
    String? displayName,
  }) = _AuthUser;
}

// lib/features/auth/domain/usecases/login_usecase.dart
class LoginUseCase implements UseCase<AuthUser, LoginParams> {
  final AuthRepository repository;
  
  LoginUseCase(this.repository);
  
  @override
  Future<Either<Failure, AuthUser>> call(LoginParams params) async {
    return await repository.login(params.email, params.password);
  }
}
```

#### 数据层 (Data)
```dart
// lib/features/auth/data/repositories/auth_repository_impl.dart
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;
  final AuthLocalDataSource localDataSource;
  
  AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });
  
  @override
  Future<Either<Failure, AuthUser>> login(String email, String password) async {
    try {
      final userModel = await remoteDataSource.login(email, password);
      await localDataSource.cacheAuthUser(userModel);
      return Right(userModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }
}
```

## 🛠️ 开发工作流

### 1. 创建新功能

#### 使用 Mason 模板
```bash
# 创建完整功能模块
mason make feature --name user_management \
  --entity true \
  --repository true \
  --usecase true \
  --bloc true \
  --page true \
  --tests true

# 创建特定组件
mason make page --name user_profile --feature user_management
mason make model --name user --feature user_management
mason make usecase --name get_user_list --feature user_management
```

#### 手动创建功能
```bash
# 1. 创建目录结构
mkdir -p lib/features/new_feature/{data,domain,presentation}

# 2. 创建实体
touch lib/features/new_feature/domain/entities/new_feature.dart

# 3. 创建用例
touch lib/features/new_feature/domain/usecases/get_new_feature.dart

# 4. 创建仓储
touch lib/features/new_feature/data/repositories/new_feature_repository_impl.dart

# 5. 创建 BLoC
touch lib/features/new_feature/presentation/bloc/new_feature_bloc.dart

# 6. 创建页面
touch lib/features/new_feature/presentation/pages/new_feature_page.dart
```

### 2. 依赖注入配置

#### 添加新的依赖
```dart
// lib/core/di/di_module.dart
class AuthModule implements DIModule {
  @override
  void configureDependencies(GetIt getIt) {
    // 数据源
    getIt.registerLazySingleton<AuthRemoteDataSource>(
      () => AuthRemoteDataSourceImpl(dio: getIt()),
    );
    
    // 仓储
    getIt.registerLazySingleton<AuthRepository>(
      () => AuthRepositoryImpl(
        remoteDataSource: getIt(),
        localDataSource: getIt(),
      ),
    );
    
    // 用例
    getIt.registerFactory<LoginUseCase>(
      () => LoginUseCase(getIt<AuthRepository>()),
    );
    
    // BLoC
    getIt.registerFactory<AuthBloc>(
      () => AuthBloc(
        loginUseCase: getIt<LoginUseCase>(),
        logoutUseCase: getIt<LogoutUseCase>(),
        getCurrentUserUseCase: getIt<GetCurrentUserUseCase>(),
      ),
    );
  }
}
```

#### 注册模块
```dart
// lib/core/di/di.dart
final getIt = GetIt.instance;

Future<void> configureDependencies() async {
  // 配置所有模块
  AuthModule().configureDependencies(getIt);
  ThemeModule().configureDependencies(getIt);
  RouterModule().configureDependencies(getIt);
  
  // 生成依赖注入代码
  await getIt.allReady();
}
```

### 3. 路由配置

#### 添加新路由
```dart
// lib/core/router/app_router.dart
class AppRouter {
  static final GoRouter _router = GoRouter(
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) => const HomePage(),
      ),
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: '/profile',
        builder: (context, state) => const ProfilePage(),
        redirect: (context, state) {
          final isAuthenticated = di.getIt<AuthBloc>().state.isAuthenticated;
          return isAuthenticated ? null : '/login';
        },
      ),
    ],
    errorBuilder: (context, state) => const ErrorPage(),
  );
  
  static GoRouter get router => _router;
}
```

#### 路由守卫
```dart
// lib/core/router/auth_guard.dart
class AuthGuard {
  static Future<bool> isAuthenticated() async {
    final authBloc = di.getIt<AuthBloc>();
    return authBloc.state.isAuthenticated;
  }
  
  static String? redirectLogic(BuildContext context, GoRouterState state) {
    final isAuthenticated = di.getIt<AuthBloc>().state.isAuthenticated;
    final isAuthRoute = state.location.startsWith('/login');
    
    if (!isAuthenticated && !isAuthRoute) {
      return '/login';
    }
    
    if (isAuthenticated && isAuthRoute) {
      return '/';
    }
    
    return null;
  }
}
```

### 4. 状态管理

#### BLoC 实现
```dart
// lib/features/auth/presentation/bloc/auth_bloc.dart
@injectable
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final LoginUseCase loginUseCase;
  final LogoutUseCase logoutUseCase;
  final GetCurrentUserUseCase getCurrentUserUseCase;
  
  AuthBloc({
    required this.loginUseCase,
    required this.logoutUseCase,
    required this.getCurrentUserUseCase,
  }) : super(const AuthState.initial()) {
    on<AuthStarted>(_onStarted);
    on<LoginRequested>(_onLoginRequested);
    on<LogoutRequested>(_onLogoutRequested);
  }
  
  Future<void> _onStarted(
    AuthStarted event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthState.loading());
    
    final result = await getCurrentUserUseCase(NoParams());
    
    result.fold(
      (failure) => emit(AuthState.unauthenticated()),
      (user) => emit(AuthState.authenticated(user)),
    );
  }
  
  Future<void> _onLoginRequested(
    LoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthState.loading());
    
    final result = await loginUseCase(
      LoginParams(email: event.email, password: event.password),
    );
    
    result.fold(
      (failure) => emit(AuthState.error(failure.message)),
      (user) => emit(AuthState.authenticated(user)),
    );
  }
}
```

#### 事件和状态
```dart
// lib/features/auth/presentation/bloc/auth_event.dart
@freezed
class AuthEvent with _$AuthEvent {
  const factory AuthEvent.started() = AuthStarted;
  const factory AuthEvent.loginRequested({
    required String email,
    required String password,
  }) = LoginRequested;
  const factory AuthEvent.logoutRequested() = LogoutRequested;
}

// lib/features/auth/presentation/bloc/auth_state.dart
@freezed
class AuthState with _$AuthState {
  const factory AuthState.initial() = _Initial;
  const factory AuthState.loading() = _Loading;
  const factory AuthState.authenticated(AuthUser user) = _Authenticated;
  const factory AuthState.unauthenticated() = _Unauthenticated;
  const factory AuthState.error(String message) = _Error;
}
```

## 🌐 网络层配置

### Dio 客户端配置
```dart
// lib/core/network/dio_client.dart
@injectable
class DioClient {
  late Dio _dio;
  
  DioClient() {
    _dio = Dio(BaseOptions(
      baseUrl: 'https://api.example.com',
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));
    
    // 添加拦截器
    _dio.interceptors.addAll([
      TokenInterceptor(),
      CacheInterceptor(),
      RetryInterceptor(),
      RequestDeduplicationInterceptor(),
    ]);
  }
  
  Dio get client => _dio;
}
```

### 拦截器实现
```dart
// lib/core/network/token_interceptor.dart
class TokenInterceptor extends Interceptor {
  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final token = await _getAuthToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }
  
  Future<String?> _getAuthToken() async {
    // 从本地存储获取 token
    return await SecureStorage.getToken();
  }
}
```

## 🗄️ 数据库配置

### Drift 数据库
```dart
// lib/core/database/app_database.dart
@DriftDatabase(tables: [Users, Products, Orders])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());
  
  @override
  int get schemaVersion => 1;
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'db.sqlite'));
    return NativeDatabase(file);
  });
}
```

### 数据表定义
```dart
// lib/features/auth/data/models/user_model.dart
class Users extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get email => text().unique()();
  TextColumn get displayName => text().nullable()();
  TextColumn get avatarUrl => text().nullable()();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get updatedAt => dateTime()();
}
```

## 🎨 主题系统

### 主题配置
```dart
// lib/core/theme/app_theme.dart
class AppTheme {
  static const _lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF1976D2),
    secondary: Color(0xFF03DAC6),
    surface: Color(0xFFFFFFFF),
    background: Color(0xFFFFFFFF),
    error: Color(0xFFB00020),
  );
  
  static const _darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFF90CAF9),
    secondary: Color(0xFF03DAC6),
    surface: Color(0xFF121212),
    background: Color(0xFF121212),
    error: Color(0xFFCF6679),
  );
  
  static ThemeData get lightTheme => ThemeData(
    colorScheme: _lightColorScheme,
    useMaterial3: true,
  );
  
  static ThemeData get darkTheme => ThemeData(
    colorScheme: _darkColorScheme,
    useMaterial3: true,
  );
}
```

### 主题切换
```dart
// lib/core/theme/theme_bloc.dart
@injectable
class ThemeBloc extends Bloc<ThemeEvent, ThemeState> {
  ThemeBloc() : super(const ThemeState.light()) {
    on<ThemeChanged>(_onThemeChanged);
    on<ThemeInitialized>(_onThemeInitialized);
  }
  
  Future<void> _onThemeInitialized(
    ThemeInitialized event,
    Emitter<ThemeState> emit,
  ) async {
    final isDark = await _getSavedTheme();
    emit(isDark ? const ThemeState.dark() : const ThemeState.light());
  }
  
  Future<void> _onThemeChanged(
    ThemeChanged event,
    Emitter<ThemeState> emit,
  ) async {
    await _saveTheme(event.isDark);
    emit(event.isDark ? const ThemeState.dark() : const ThemeState.light());
  }
}
```

## 🧪 测试策略

### 单元测试
```dart
// lib/features/auth/domain/usecases/login_usecase_test.dart
void main() {
  late LoginUseCase loginUseCase;
  late MockAuthRepository mockAuthRepository;
  
  setUp(() {
    mockAuthRepository = MockAuthRepository();
    loginUseCase = LoginUseCase(mockAuthRepository);
  });
  
  test('should login successfully with valid credentials', () async {
    // Arrange
    const params = LoginParams(email: '<EMAIL>', password: 'password');
    const expectedUser = AuthUser(id: '1', email: '<EMAIL>');
    
    when(mockAuthRepository.login('<EMAIL>', 'password'))
        .thenAnswer((_) async => const Right(expectedUser));
    
    // Act
    final result = await loginUseCase(params);
    
    // Assert
    expect(result, const Right(expectedUser));
    verify(mockAuthRepository.login('<EMAIL>', 'password')).called(1);
  });
}
```

### Widget 测试
```dart
// lib/features/auth/presentation/pages/login_page_test.dart
void main() {
  late MockAuthBloc mockAuthBloc;
  
  setUp(() {
    mockAuthBloc = MockAuthBloc();
  });
  
  Widget createWidget() {
    return MaterialApp(
      home: BlocProvider<AuthBloc>.value(
        value: mockAuthBloc,
        child: const LoginPage(),
      ),
    );
  }
  
  testWidgets('should show login form', (WidgetTester tester) async {
    // Arrange
    whenListen(
      mockAuthBloc,
      const Stream<AuthState>.empty(),
      const AuthState.initial(),
    );
    
    // Act
    await tester.pumpWidget(createWidget());
    
    // Assert
    expect(find.byType(TextFormField), findsNWidgets(2));
    expect(find.byType(ElevatedButton), findsOneWidget);
  });
}
```

## 🔧 调试和优化

### 性能监控
```dart
// lib/core/utils/performance_monitor.dart
class PerformanceMonitor {
  static final _observer = _PerformanceObserver();
  
  static void start() {
    FlutterPerformance.instance.register(_observer);
  }
  
  static void stop() {
    FlutterPerformance.instance.unregister(_observer);
  }
}

class _PerformanceObserver extends FlutterPerformanceObserver {
  @override
  void onFrameTimings(List<FrameTiming> timings) {
    for (final timing in timings) {
      if (timing.durationInMs > 16) {
        debugPrint('Frame dropped: ${timing.durationInMs}ms');
      }
    }
  }
}
```

### 错误处理
```dart
// lib/core/error/global_error_handler.dart
class GlobalErrorHandler {
  static void init() {
    FlutterError.onError = (details) {
      // 记录错误到 Sentry 或其他服务
      Sentry.captureException(details.exception, stackTrace: details.stack);
      debugPrint('Flutter Error: ${details.exception}');
    };
    
    PlatformDispatcher.instance.onError = (error, stack) {
      // 记录平台错误
      Sentry.captureException(error, stackTrace: stack);
      debugPrint('Platform Error: $error');
      return true;
    };
  }
}
```

## 📦 构建和部署

### 开发构建
```bash
# 开发环境
flutter run --flavor development -t lib/main_development.dart

# 热重载
flutter run --hot --flavor development
```

### 生产构建
```bash
# Android APK
flutter build apk --flavor production --release --shrink --obfuscate

# Android AAB
flutter build appbundle --flavor production --release

# iOS
flutter build ios --flavor production --release --no-codesign
```

### Fastlane 部署
```ruby
# fastlane/Fastfile
lane :deploy_production do
  build_ios_app(
    scheme: 'production',
    workspace: 'Runner.xcworkspace',
    output_directory: '../build/ios'
  )
  
  upload_to_app_store(
    api_key_path: './AppStoreConnect_API_Key.json'
  )
end
```

## 🎯 最佳实践

### 代码规范
- 使用 `dart format` 格式化代码
- 遵循 `flutter analyze` 的静态分析规则
- 使用 `freezed` 进行数据类生成
- 保持函数单一职责

### 性能优化
- 使用 `const` 构造函数
- 合理使用 `ListView.builder`
- 图片使用 `cached_network_image`
- 避免不必要的重建

### 安全考虑
- 敏感信息使用 `flutter_secure_storage`
- 网络请求使用 HTTPS
- 输入验证和清理
- 定期更新依赖包

## 📚 相关资源

- [Flutter 官方文档](https://flutter.dev/docs)
- [Clean Architecture 指南](docs/CLEAN_ARCHITECTURE_GUIDE_PRO.md)
- [状态管理指南](docs/STATE_MANAGEMENT_GUIDE.md)
- [Mason 使用指南](docs/MASON_GUIDE.md)
- [UI 设计指南](docs/UI_GUIDE_PRO.md)