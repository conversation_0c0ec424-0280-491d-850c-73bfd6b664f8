import 'package:equatable/equatable.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
abstract class User extends Equatable with _$User {
  const factory User({
    required String id,
    required String email,
    String? name,
    String? phone,
    String? avatar,
    @JsonKey(name: 'created_at') DateTime? createdAt,
    @J<PERSON><PERSON><PERSON>(name: 'updated_at') DateTime? updatedAt,
    // Auth-related fields (not serialized to JSON by default)
    @JsonKey(includeFromJson: false, includeToJson: false) String? accessToken,
    @JsonKey(includeFromJson: false, includeToJson: false) String? refreshToken,
    @JsonKey(includeFromJson: false, includeToJson: false) String? tokenExpiry,
  }) = _User;

  const User._();

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  @override
  List<Object?> get props => [id, email, name, phone, avatar, createdAt, updatedAt, accessToken, refreshToken, tokenExpiry];

  /// Create a copy with auth tokens
  User withTokens({String? accessToken, String? refreshToken}) {
    return copyWith(
      accessToken: accessToken,
      refreshToken: refreshToken,
    );
  }

  /// Create a copy without auth tokens (for serialization)
  User withoutTokens() {
    return copyWith(
      accessToken: null,
      refreshToken: null,
    );
  }

  /// Check if user is authenticated
  bool get isAuthenticated => accessToken?.isNotEmpty == true;
}