import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import '../../../../presentation/bloc/{{name.snakeCase}}_bloc.dart';
import '../../../../presentation/pages/{{name.snakeCase}}_page.dart';

class Mock{{name.pascalCase}}Bloc extends Mock implements {{name.pascalCase}}Bloc {}

void main() {
  late Mock{{name.pascalCase}}Bloc mock{{name.pascalCase}}Bloc;

  setUp(() {
    mock{{name.pascalCase}}Bloc = Mock{{name.pascalCase}}Bloc();
  });

  testWidgets('{{name.pascalCase}}Page displays loading indicator when state is loading', (tester) async {
    // arrange
    when(() => mock{{name.pascalCase}}Bloc.state).thenReturn(const {{name.pascalCase}}State.loading());

    // act
    await tester.pumpWidget(
      MaterialApp(
        home: BlocProvider.value(
          value: mock{{name.pascalCase}}Bloc,
          child: const {{name.pascalCase}}Page(),
        ),
      ),
    );

    // assert
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  });

  testWidgets('{{name.pascalCase}}Page displays error message when state is error', (tester) async {
    // arrange
    const errorMessage = 'Error occurred';
    when(() => mock{{name.pascalCase}}Bloc.state).thenReturn(const {{name.pascalCase}}State.error(errorMessage));

    // act
    await tester.pumpWidget(
      MaterialApp(
        home: BlocProvider.value(
          value: mock{{name.pascalCase}}Bloc,
          child: const {{name.pascalCase}}Page(),
        ),
      ),
    );

    // assert
    expect(find.text(errorMessage), findsOneWidget);
  });
}