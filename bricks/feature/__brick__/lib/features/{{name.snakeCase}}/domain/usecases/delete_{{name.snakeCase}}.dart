import 'package:fpdart/fpdart.dart';
import '../../../../../core/error/failure.dart';
import '../../../../../core/usecase/usecase.dart';
import '../../domain/repositories/{{name.snakeCase}}_repository.dart';

class Delete{{name.pascalCase}}UseCase implements UseCase<void, Delete{{name.pascalCase}}Params> {
  final {{name.pascalCase}}Repository repository;

  const Delete{{name.pascalCase}}UseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(Delete{{name.pascalCase}}Params params) async {
    return await repository.delete{{name.pascalCase}}(params.id);
  }
}

class Delete{{name.pascalCase}}Params {
  final String id;

  Delete{{name.pascalCase}}Params({required this.id});
}