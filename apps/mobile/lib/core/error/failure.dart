import 'package:equatable/equatable.dart';
import 'package:fpdart/fpdart.dart';

sealed class Failure extends Equatable {
  final String message;
  final int? code;

  const Failure({required this.message, this.code});

  @override
  List<Object?> get props => [message, code];
}

class ServerFailure extends Failure {
  const ServerFailure({required super.message, super.code});
}

class CacheFailure extends Failure {
  const CacheFailure({required super.message, super.code = 500});
}

class NetworkFailure extends Failure {
  const NetworkFailure({required super.message, super.code = 503});
}

class ValidationFailure extends Failure {
  const ValidationFailure({required super.message, super.code = 400});
}

class UnauthorizedFailure extends Failure {
  const UnauthorizedFailure({required super.message, super.code = 401});
}

class NotFoundFailure extends Failure {
  const NotFoundFailure({required super.message, super.code = 404});
}

class UnknownFailure extends Failure {
  const UnknownFailure({required super.message, super.code = 500});
}

typedef ResultFuture<T> = Future<Either<Failure, T>>;
typedef ResultVoid = ResultFuture<void>;
typedef StreamResult<T> = Stream<Either<Failure, T>>;