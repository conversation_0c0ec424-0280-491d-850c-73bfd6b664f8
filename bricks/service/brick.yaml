name: service
description: Creates a new service component
version: 0.1.0

vars:
  name:
    type: string
    description: Service name
    default: example
    prompt: What is the name of the service?
  
  feature:
    type: string
    description: Feature name
    default: core
    prompt: What feature does this service belong to?
  
  type:
    type: string
    description: Service type (background, notification, platform)
    default: background
    prompt: What type of service? (background/notification/platform)