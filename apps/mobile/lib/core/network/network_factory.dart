import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../shared/constants.dart';
import 'cache_interceptor.dart';
import 'request_deduplication_interceptor.dart';
import 'retry_interceptor.dart';
import 'token_interceptor.dart';

/// Factory class for creating configured Dio instances
/// Centralizes network configuration to avoid duplication
class NetworkFactory {
  /// Creates a configured Dio instance with all necessary interceptors
  static Dio createConfiguredDio(SharedPreferences prefs) {
    final dio = Dio(BaseOptions(
      baseUrl: AppConstants.baseUrl,
      connectTimeout: AppConstants.connectTimeout,
      receiveTimeout: AppConstants.receiveTimeout,
      sendTimeout: AppConstants.sendTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors in the correct order
    dio.interceptors.addAll([
      const RetryInterceptor(),                     // Retry logic first
      TokenInterceptor(prefs, dio),           // Token management
      CacheInterceptor(),                     // Cache strategy
      RequestDeduplicationInterceptor(),      // Request deduplication
      if (kDebugMode)
        PrettyDioLogger(                      // Debug logging last
          requestHeader: true,
          requestBody: true,
          responseBody: true,
          responseHeader: false,
          error: true,
          compact: true,
          maxWidth: 90,
        ),
    ]);

    return dio;
  }
}