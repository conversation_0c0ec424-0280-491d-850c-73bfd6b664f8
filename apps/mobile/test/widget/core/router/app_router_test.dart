import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mocktail/mocktail.dart';

import 'package:flutter_scaffold_mobile/core/router/app_router.dart';
import 'package:flutter_scaffold_mobile/features/auth/presentation/bloc/auth_bloc.dart';

class MockAuthBloc extends Mock implements AuthBloc {
  final _completer = Completer<void>();

  @override
  Future<void> close() {
    if (!_completer.isCompleted) {
      _completer.complete();
    }
    return _completer.future;
  }
}

void main() {
  group('App Router Tests', () {
    late MockAuthBloc mockAuthBloc;

    setUp(() {
      mockAuthBloc = MockAuthBloc();
      when(() => mockAuthBloc.state).thenReturn(const AuthState());
      when(() => mockAuthBloc.stream).thenAnswer((_) => Stream.value(const AuthState()));
    });

    testWidgets('App should render with router configuration', (tester) async {
      await tester.pumpWidget(
        BlocProvider<AuthBloc>(
          create: (_) => mockAuthBloc,
          child: MaterialApp.router(
            routerConfig: AppRouter.router,
          ),
        ),
      );

      // Wait for the app to settle
      await tester.pumpAndSettle();

      // Verify the app renders without crashing
      expect(find.byType(MaterialApp), findsOneWidget);
    });


  });
}