import 'package:dio/dio.dart';

class RequestDeduplicationInterceptor extends Interceptor {
  final Map<String, Future<Response>> _pendingRequests = {};

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final key = _generateRequestKey(options);
    
    // Check if there's already a pending request with the same key
    if (_pendingRequests.containsKey(key)) {
      try {
        final response = await _pendingRequests[key]!;
        handler.resolve(response);
        return;
      } catch (e) {
        // If the pending request failed, continue with the new request
        _pendingRequests.remove(key);
      }
    }
    
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    final key = _generateRequestKey(response.requestOptions);
    _pendingRequests.remove(key);
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final key = _generateRequestKey(err.requestOptions);
    _pendingRequests.remove(key);
    handler.next(err);
  }

  String _generateRequestKey(RequestOptions options) {
    // Generate a unique key based on method, path, and query parameters
    final buffer = StringBuffer();
    buffer.write(options.method.toUpperCase());
    buffer.write('|');
    buffer.write(options.path);
    
    if (options.queryParameters.isNotEmpty) {
      buffer.write('|');
      final sortedParams = Map.fromEntries(
        options.queryParameters.entries.toList()
          ..sort((a, b) => a.key.compareTo(b.key)),
      );
      buffer.write(sortedParams.toString());
    }
    
    return buffer.toString();
  }

  void addPendingRequest(RequestOptions options, Future<Response> future) {
    final key = _generateRequestKey(options);
    _pendingRequests[key] = future;
  }
}