import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:fpdart/fpdart.dart';

import 'package:flutter_scaffold_mobile/features/auth/domain/entities/auth_user.dart';
import 'package:flutter_scaffold_mobile/features/auth/domain/repositories/auth_repository.dart';
import 'package:flutter_scaffold_mobile/features/auth/domain/usecases/login_usecase.dart';
import 'package:flutter_scaffold_mobile/core/error/failure.dart';

class MockAuthRepository extends Mock implements AuthRepository {}

void main() {
  late LoginUseCase loginUseCase;
  late MockAuthRepository mockAuthRepository;

  setUp(() {
    mockAuthRepository = MockAuthRepository();
    loginUseCase = LoginUseCase(mockAuthRepository);
  });

  group('LoginUseCase', () {
    const testEmail = '<EMAIL>';
    const testPassword = 'password123';
    const testAuthUser = AuthUser(
      id: '1',
      email: testEmail,
      name: 'Test User',
    );

    test('should return AuthUser when login is successful', () async {
      // Arrange
      when(() => mockAuthRepository.login(testEmail, testPassword))
          .thenAnswer((_) async => const Right(testAuthUser));

      // Act
      final result = await loginUseCase(
        const LoginParams(email: testEmail, password: testPassword),
      );

      // Assert
      expect(result, const Right(testAuthUser));
      verify(() => mockAuthRepository.login(testEmail, testPassword)).called(1);
      verifyNoMoreInteractions(mockAuthRepository);
    });

    test('should return ServerFailure when login fails', () async {
      // Arrange
      const failure = ServerFailure(message: 'Invalid credentials');
      when(() => mockAuthRepository.login(testEmail, testPassword))
          .thenAnswer((_) async => const Left(failure));

      // Act
      final result = await loginUseCase(
        const LoginParams(email: testEmail, password: testPassword),
      );

      // Assert
      expect(result, const Left(failure));
      verify(() => mockAuthRepository.login(testEmail, testPassword)).called(1);
      verifyNoMoreInteractions(mockAuthRepository);
    });
  });
}