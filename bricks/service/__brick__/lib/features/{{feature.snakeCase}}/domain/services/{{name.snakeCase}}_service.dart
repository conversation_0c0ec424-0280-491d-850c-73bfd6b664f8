{{#isBackground}}
import 'dart:async';
import 'package:flutter/foundation.dart';

class {{name.pascalCase}}Service {
  final StreamController<String> _controller = StreamController<String>.broadcast();
  late Timer _timer;

  {{name.pascalCase}}Service() {
    _startBackgroundTask();
  }

  Stream<String> get onData => _controller.stream;

  void _startBackgroundTask() {
    _timer = Timer.periodic(const Duration(seconds: 30), (timer) {
      // TODO: Implement background task logic
      debugPrint('{{name.pascalCase}}Service: Performing background task');
      _controller.add('{{name.pascalCase}}Service: Background task executed');
    });
  }

  void dispose() {
    _timer.cancel();
    _controller.close();
  }
}
{{/isBackground}}

{{#isNotification}}
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class {{name.pascalCase}}NotificationService {
  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  {{name.pascalCase}}NotificationService() {
    _initializeNotifications();
  }

  Future<void> _initializeNotifications() async {
    // TODO: Implement notification initialization
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('app_icon');
        
    final InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);
        
    await _notificationsPlugin.initialize(initializationSettings);
  }

  Future<void> showNotification(String title, String body) async {
    // TODO: Implement notification display
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      '{{name.snakeCase}}_channel',
      '{{name.pascalCase}} Channel',
      channelDescription: '{{name.pascalCase}} notifications',
      importance: Importance.max,
      priority: Priority.high,
    );
    
    const NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);
        
    await _notificationsPlugin.show(
      0,
      title,
      body,
      platformChannelSpecifics,
    );
  }
}
{{/isNotification}}

{{#isPlatform}}
import 'package:flutter/services.dart';

class {{name.pascalCase}}PlatformService {
  static const MethodChannel _channel = MethodChannel('{{name.snakeCase}}_channel');

  {{name.pascalCase}}PlatformService();

  Future<String?> getPlatformData() async {
    // TODO: Implement platform-specific logic
    try {
      final String? result = await _channel.invokeMethod('getPlatformData');
      return result;
    } on PlatformException catch (e) {
      debugPrint('Failed to get platform data: ${e.message}');
      return null;
    }
  }

  Future<bool> performPlatformAction() async {
    // TODO: Implement platform-specific action
    try {
      final bool result = await _channel.invokeMethod('performPlatformAction');
      return result;
    } on PlatformException catch (e) {
      debugPrint('Failed to perform platform action: ${e.message}');
      return false;
    }
  }
}
{{/isPlatform}}