# 001 - 采用BLoC作为状态管理方案

* **状态：** 已采纳
* **日期：** 2024-01-15

## 背景

我们需要为Flutter应用选择一个健壮的状态管理方案，需满足：
- 可预测的状态流转
- 优秀的可测试性
- 支持复杂应用的扩展性
- 良好的开发体验和工具链

## 决策

我们决定采用Flutter BLoC（Business Logic Component）作为核心的状态管理方案。

## 决策依据

1. **可预测性**：BLoC强制单向数据流，状态变化清晰可追踪
2. **可测试性**：事件和状态都是纯对象，单元测试轻而易举
3. **扩展性**：天然支持复杂应用和嵌套导航场景
4. **开发体验**：完善的工具链和开发文档
5. **社区生态**：活跃的社区和丰富的周边生态

## 影响后果

### 积极影响
- 应用状态管理风格统一
- 业务逻辑测试变得简单
- 关注点分离清晰明了
- IDE支持完善

### 消极影响
- 团队成员需要学习成本
- 相比简单方案代码量更多
- 对于简单应用可能过重