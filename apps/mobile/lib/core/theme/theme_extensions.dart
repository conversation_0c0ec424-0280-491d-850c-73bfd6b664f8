import 'package:flutter/material.dart';

/// 主题扩展工具类
///
/// 提供便捷的主题访问方法，统一使用 Material 3 ColorScheme
/// 避免直接使用废弃的 primaryColor 等属性
extension ThemeExtensions on ThemeData {
  /// 获取主要颜色 - 替代废弃的 primaryColor
  Color get primaryColor => colorScheme.primary;

  /// 获取主要颜色的容器色
  Color get primaryContainer => colorScheme.primaryContainer;

  /// 获取次要颜色 - 替代废弃的 accentColor
  Color get secondaryColor => colorScheme.secondary;

  /// 获取次要颜色的容器色
  Color get secondaryContainer => colorScheme.secondaryContainer;

  /// 获取表面颜色 - 替代废弃的 backgroundColor
  Color get surfaceColor => colorScheme.surface;

  /// 获取背景颜色
  Color get backgroundColor => colorScheme.surface;

  /// 获取错误颜色
  Color get errorColor => colorScheme.error;

  /// 获取在主要颜色上的文字颜色
  Color get onPrimaryColor => colorScheme.onPrimary;

  /// 获取在次要颜色上的文字颜色
  Color get onSecondaryColor => colorScheme.onSecondary;

  /// 获取在表面上的文字颜色
  Color get onSurfaceColor => colorScheme.onSurface;

  /// 获取在背景上的文字颜色
  Color get onBackgroundColor => colorScheme.onSurface;
}

/// 颜色扩展工具类
///
/// 提供高性能的颜色透明度操作，替代 withOpacity()
extension ColorExtensions on Color {
  /// 高性能透明度设置 - 替代 withOpacity()
  ///
  /// 使用 Color.fromARGB 避免运行时计算
  Color withAlpha(double opacity) {
    assert(opacity >= 0.0 && opacity <= 1.0);
    return Color.fromARGB(
      (255 * opacity).round(),
      (r * 255.0).round() & 0xff,
      (g * 255.0).round() & 0xff,
      (b * 255.0).round() & 0xff,
    );
  }

  /// 常用透明度预设
  Color get withLowOpacity => withAlpha(0.1);
  Color get withMediumOpacity => withAlpha(0.3);
  Color get withHighOpacity => withAlpha(0.7);
  Color get withVeryHighOpacity => withAlpha(0.9);
}

/// 主题颜色预设
///
/// 提供常用的颜色组合，避免重复的透明度计算
class ThemeColors {
  const ThemeColors._();

  /// 根据主题获取预设颜色
  static ThemeColorSet of(BuildContext context) {
    final theme = Theme.of(context);
    return ThemeColorSet._(theme);
  }
}

/// 主题颜色集合
class ThemeColorSet {
  const ThemeColorSet._(this._theme);

  final ThemeData _theme;

  /// 主要颜色相关
  Color get primary => _theme.colorScheme.primary;
  Color get primaryLight => _theme.colorScheme.primary.withAlpha((255 * 0.1).round());
  Color get primaryMedium => _theme.colorScheme.primary.withAlpha((255 * 0.3).round());
  Color get primaryShadow => _theme.colorScheme.primary.withAlpha((255 * 0.3).round());
  Color get primaryGradientStart => _theme.colorScheme.primary;
  Color get primaryGradientEnd => _theme.colorScheme.primary.withAlpha((255 * 0.7).round());

  /// 表面颜色相关
  Color get surface => _theme.colorScheme.surface;
  Color get surfaceVariant => _theme.colorScheme.surfaceContainerHighest;
  Color get onSurface => _theme.colorScheme.onSurface;
  Color get onSurfaceVariant => _theme.colorScheme.onSurfaceVariant;

  /// 阴影颜色
  Color get shadow => Colors.black.withAlpha((255 * 0.1).round());
  Color get shadowMedium => Colors.black.withAlpha((255 * 0.15).round());
  Color get shadowStrong => Colors.black.withAlpha((255 * 0.3).round());

  /// 边框颜色
  Color get border => _theme.colorScheme.outline;
  Color get borderLight => _theme.colorScheme.outline.withAlpha((255 * 0.3).round());

  /// 状态颜色
  Color get success => Colors.green[400]!;
  Color get warning => Colors.orange[400]!;
  Color get error => _theme.colorScheme.error;
  Color get info => Colors.blue[400]!;
}

/// 文本样式扩展
///
/// 提供便捷的文本样式访问，确保使用正确的 Material 3 命名
extension TextThemeExtensions on TextTheme {
  /// 大标题样式 - 替代废弃的 headline1
  TextStyle? get displayLargeStyle => displayLarge;

  /// 中标题样式 - 替代废弃的 headline2
  TextStyle? get displayMediumStyle => displayMedium;

  /// 小标题样式 - 替代废弃的 headline3
  TextStyle? get displaySmallStyle => displaySmall;

  /// 页面标题样式 - 替代废弃的 headline6
  TextStyle? get titleLargeStyle => titleLarge;

  /// 卡片标题样式 - 替代废弃的 subtitle1
  TextStyle? get titleMediumStyle => titleMedium;

  /// 小标题样式 - 替代废弃的 subtitle2
  TextStyle? get titleSmallStyle => titleSmall;

  /// 正文大样式 - 替代废弃的 bodyText1
  TextStyle? get bodyLargeStyle => bodyLarge;

  /// 正文样式 - 替代废弃的 bodyText2
  TextStyle? get bodyMediumStyle => bodyMedium;

  /// 说明文字样式 - 替代废弃的 caption
  TextStyle? get bodySmallStyle => bodySmall;

  /// 按钮文字样式 - 替代废弃的 button
  TextStyle? get labelLargeStyle => labelLarge;

  /// 小标签样式 - 替代废弃的 overline
  TextStyle? get labelSmallStyle => labelSmall;
}
