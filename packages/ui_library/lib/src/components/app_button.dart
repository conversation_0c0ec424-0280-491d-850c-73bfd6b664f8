import 'package:flutter/material.dart';

class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonStyle? style;
  final bool isLoading;
  final Widget? icon;
  final bool isOutlined;
  final bool isText;
  final double? width;
  final double? height;

  const AppButton({
    super.key,
    required this.text,
    this.onPressed,
    this.style,
    this.isLoading = false,
    this.icon,
    this.isOutlined = false,
    this.isText = false,
    this.width,
    this.height,
  });

  const AppButton.outlined({
    super.key,
    required this.text,
    this.onPressed,
    this.style,
    this.isLoading = false,
    this.icon,
    this.width,
    this.height,
  })  : isOutlined = true,
        isText = false;

  const AppButton.text({
    super.key,
    required this.text,
    this.onPressed,
    this.style,
    this.isLoading = false,
    this.icon,
    this.width,
    this.height,
  })  : isOutlined = false,
        isText = true;

  @override
  Widget build(BuildContext context) {
    final ButtonStyle defaultStyle = isOutlined
        ? OutlinedButton.styleFrom(
            minimumSize: Size(width ?? 88, height ?? 36),
          )
        : isText
            ? TextButton.styleFrom(
                minimumSize: Size(width ?? 88, height ?? 36),
              )
            : ElevatedButton.styleFrom(
                minimumSize: Size(width ?? 88, height ?? 36),
              );

    final Widget buttonContent = Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (icon != null) ...[
          icon!,
          const SizedBox(width: 8),
        ],
        if (isLoading)
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          )
        else
          Text(text),
      ],
    );

    if (isOutlined) {
      return OutlinedButton(
        onPressed: isLoading ? null : onPressed,
        style: (style ?? defaultStyle).copyWith(
          minimumSize: WidgetStateProperty.all(Size(width ?? 88, height ?? 36)),
        ),
        child: buttonContent,
      );
    } else if (isText) {
      return TextButton(
        onPressed: isLoading ? null : onPressed,
        style: (style ?? defaultStyle).copyWith(
          minimumSize: WidgetStateProperty.all(Size(width ?? 88, height ?? 36)),
        ),
        child: buttonContent,
      );
    } else {
      return ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: (style ?? defaultStyle).copyWith(
          minimumSize: WidgetStateProperty.all(Size(width ?? 88, height ?? 36)),
        ),
        child: buttonContent,
      );
    }
  }
}