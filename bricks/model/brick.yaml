name: model
description: Creates a new data model with entity and model classes
version: 0.1.0

vars:
  name:
    type: string
    description: Model name
    default: example
    prompt: What is the name of the model?
  
  feature:
    type: string
    description: Feature name
    default: core
    prompt: What feature does this model belong to?
  
  fields:
    type: string
    description: Model fields (comma separated, e.g. "name:string,age:int")
    default: ""
    prompt: What fields should the model have? (comma separated, e.g. "name:string,age:int")