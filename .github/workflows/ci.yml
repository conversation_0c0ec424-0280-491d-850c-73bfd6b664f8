name: Flutter CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  FLUTTER_VERSION: "3.16.0"

jobs:
  analyze:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: "stable"

      - name: Setup Melos
        uses: bluefireteam/melos-action@v3
        with:
          run-bootstrap: false

      - name: Install dependencies
        run: melos bootstrap

      - name: Analyze packages
        run: melos run analyze

      - name: Check formatting
        run: melos run format

  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        package:
          - apps/mobile
          - packages/ui_library
          - packages/data_models

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: "stable"

      - name: <PERSON>up <PERSON>
        uses: bluefireteam/melos-action@v3

      - name: Run tests
        run: |
          cd ${{ matrix.package }}
          flutter test --coverage --test-randomize-ordering-seed random

      - name: Upload coverage
        if: matrix.package == 'apps/mobile'
        uses: codecov/codecov-action@v3
        with:
          file: apps/mobile/coverage/lcov.info
          flags: mobile

  build-android:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/main'
    needs: [analyze, test]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: "stable"

      - name: Setup Melos
        uses: bluefireteam/melos-action@v3

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: "zulu"
          java-version: "17"

      - name: Build APK
        run: |
          cd apps/mobile
          flutter build apk --release --flavor production -t lib/main_production.dart

      - name: Upload APK
        uses: actions/upload-artifact@v4
        with:
          name: android-apk
          path: apps/mobile/build/app/outputs/flutter-apk/app-release.apk

  build-ios:
    runs-on: macos-latest
    if: github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/main'
    needs: [analyze, test]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: "stable"

      - name: Setup Melos
        uses: bluefireteam/melos-action@v3

      - name: Build iOS
        run: |
          cd apps/mobile
          flutter build ios --release --no-codesign --flavor production -t lib/main_production.dart

  deploy-staging:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    needs: [build-android]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Download APK
        uses: actions/download-artifact@v4
        with:
          name: android-apk
          path: apps/mobile/build/app/outputs/flutter-apk/

      - name: Deploy to Firebase App Distribution
        uses: wzieba/Firebase-Distribution-Github-Action@v1
        with:
          appId: ${{ secrets.FIREBASE_APP_ID_STAGING }}
          token: ${{ secrets.FIREBASE_TOKEN }}
          groups: testers
          file: apps/mobile/build/app/outputs/flutter-apk/app-release.apk

  deploy-production:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    needs: [build-android, build-ios]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Fastlane
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: "3.0"
          bundler-cache: true

      - name: Deploy to Play Store
        env:
          PLAY_STORE_CONFIG_JSON: ${{ secrets.PLAY_STORE_CONFIG_JSON }}
        run: |
          cd apps/android
          echo $PLAY_STORE_CONFIG_JSON | base64 -d > play-store-config.json
          bundle exec fastlane android deploy

  security-scan:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: "fs"
          scan-ref: "."
          format: "sarif"
          output: "trivy-results.sarif"

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: "trivy-results.sarif"