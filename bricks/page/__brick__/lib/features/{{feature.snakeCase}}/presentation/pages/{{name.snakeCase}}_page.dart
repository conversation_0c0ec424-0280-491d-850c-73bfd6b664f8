import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/{{name.snakeCase}}_bloc.dart';
import '../widgets/{{name.snakeCase}}_widget.dart';

class {{name.pascalCase}}Page extends StatelessWidget {
  const {{name.pascalCase}}Page({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('{{name.pascalCase}}'),
      ),
      body: BlocBuilder<{{name.pascalCase}}Bloc, {{name.pascalCase}}State>(
        builder: (context, state) {
          return state.when(
            initial: () => const SizedBox(),
            loading: () => const Center(child: CircularProgressIndicator()),
            loaded: ({{entity.camelCase}}) => {{name.pascalCase}}Widget({{entity.camelCase}}: {{entity.camelCase}}),
            loadedAll: ({{entity.camelCase}}s) => ListView.builder(
              itemCount: {{entity.camelCase}}s.length,
              itemBuilder: (context, index) {
                return {{name.pascalCase}}Widget({{entity.camelCase}}: {{entity.camelCase}}s[index]);
              },
            ),
            error: (message) => Center(child: Text(message)),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.read<{{name.pascalCase}}Bloc>().add(const {{name.pascalCase}}Event.load{{entity.pascalCase}}s());
        },
        child: const Icon(Icons.refresh),
      ),
    );
  }
}