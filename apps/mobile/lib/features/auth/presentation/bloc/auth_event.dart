part of 'auth_bloc.dart';

sealed class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object> get props => [];
}

final class AuthStarted extends AuthEvent {
  const AuthStarted();
}

final class AuthLoginRequested extends AuthEvent {
  final String email;
  final String password;

  const AuthLoginRequested({
    required this.email,
    required this.password,
  });

  @override
  List<Object> get props => [email, password];
}

final class AuthSmsLoginRequested extends AuthEvent {
  final String phone;
  final String code;

  const AuthSmsLoginRequested({
    required this.phone,
    required this.code,
  });

  @override
  List<Object> get props => [phone, code];
}

final class AuthSendSmsCodeRequested extends AuthEvent {
  final String phone;

  const AuthSendSmsCodeRequested({
    required this.phone,
  });

  @override
  List<Object> get props => [phone];
}

final class AuthWechatLoginRequested extends AuthEvent {
  const AuthWechatLoginRequested();
}

final class AuthLogoutRequested extends AuthEvent {
  const AuthLogoutRequested();
}

final class AuthUserUpdated extends AuthEvent {
  final AuthUser? user;

  const AuthUserUpdated(this.user);

  @override
  List<Object> get props => [user ?? 'null'];
}