import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../network/network_factory.dart';

@module
abstract class InjectableModule {
  @lazySingleton
  Dio dio(SharedPreferences prefs) => NetworkFactory.createConfiguredDio(prefs);

  @preResolve
  Future<SharedPreferences> get prefs => SharedPreferences.getInstance();
}
