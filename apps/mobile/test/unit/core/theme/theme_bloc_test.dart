import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';

import 'package:flutter_scaffold_mobile/core/theme/theme_bloc.dart';

class MockSharedPreferences extends Mo<PERSON> implements SharedPreferences {}

void main() {
  group('ThemeBloc', () {
    late ThemeBloc themeBloc;
    late MockSharedPreferences mockPrefs;

    setUp(() {
      mockPrefs = MockSharedPreferences();
      themeBloc = ThemeBloc(mockPrefs);
    });

    tearDown(() {
      themeBloc.close();
    });

    test('initial state is system theme', () {
      expect(themeBloc.state.themeMode, ThemeMode.system);
    });

    blocTest<ThemeBloc, ThemeState>(
      'emits saved theme when ThemeInitialized is added',
      build: () {
        when(() => mockPrefs.getInt('theme_mode')).thenReturn(ThemeMode.dark.index);
        return ThemeBloc(mockPrefs);
      },
      act: (bloc) => bloc.add(ThemeInitialized()),
      expect: () => [
        const ThemeState(themeMode: ThemeMode.dark),
      ],
    );

    blocTest<ThemeBloc, ThemeState>(
      'emits new theme when ThemeChanged is added',
      build: () {
        when(() => mockPrefs.setInt(any(), any())).thenAnswer((_) async => true);
        return ThemeBloc(mockPrefs);
      },
      act: (bloc) => bloc.add(ThemeChanged(ThemeMode.light)),
      expect: () => [
        const ThemeState(themeMode: ThemeMode.light),
      ],
      verify: (_) {
        verify(() => mockPrefs.setInt('theme_mode', ThemeMode.light.index)).called(1);
      },
    );
  });
}