import 'package:equatable/equatable.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/error/failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../entities/auth_user.dart';
import '../repositories/auth_repository.dart';

class SmsLoginParams extends Equatable {
  final String phone;
  final String code;

  const SmsLoginParams({
    required this.phone,
    required this.code,
  });

  @override
  List<Object> get props => [phone, code];
}

@injectable
class SmsLoginUseCase implements UseCase<AuthUser, SmsLoginParams> {
  final AuthRepository repository;

  const SmsLoginUseCase(this.repository);

  @override
  Future<Either<Failure, AuthUser>> call(SmsLoginParams params) async {
    return await repository.smsLogin(
      phone: params.phone,
      code: params.code,
    );
  }
}