import 'package:flutter/material.dart';
{{#bloc}}
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/{{entity.snakeCase()}}_bloc.dart';
{{/bloc}}

class {{name.pascalCase()}}Page extends StatelessWidget {
  const {{name.pascalCase()}}Page({super.key});

  @override
  Widget build(BuildContext context) {
    {{#bloc}}
    return BlocProvider(
      create: (context) => {{entity.pascalCase()}}Bloc(
        get{{entity.pascalCase()}}Usecase: context.read(),
        create{{entity.pascalCase()}}Usecase: context.read(),
        update{{entity.pascalCase()}}Usecase: context.read(),
        delete{{entity.pascalCase()}}Usecase: context.read(),
      )..add(const {{entity.pascalCase()}}Event.started()),
      child: const {{name.pascalCase()}}View(),
    );
    {{/bloc}}
    {{^bloc}}
    return const {{name.pascalCase()}}View();
    {{/bloc}}
  }
}

class {{name.pascalCase()}}View extends StatelessWidget {
  const {{name.pascalCase()}}View({super.key});

  @override
  Widget build(BuildContext context) {
    {{#bloc}}
    return BlocConsumer<{{entity.pascalCase()}}Bloc, {{entity.pascalCase()}}State>(
      listener: (context, state) {
        state.maybeMap(
          error: (error) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(error.message)),
            );
          },
          orElse: () {},
        );
      },
      builder: (context, state) {
        return state.maybeMap(
          initial: (_) => const _InitialWidget(),
          loading: (_) => const _LoadingWidget(),
          loaded: (loaded) => _ContentWidget({{entity.camelCase()}}: loaded.{{entity.camelCase()}}),
          listLoaded: (listLoaded) => _ListContentWidget({{entity.camelCase()}}s: listLoaded.{{entity.camelCase()}}s),
          error: (error) => _ErrorWidget(message: error.message),
          orElse: () => const _InitialWidget(),
        );
      },
    );
    {{/bloc}}
    {{^bloc}}
    return const Scaffold(
      body: Center(
        child: Text('{{name.pascalCase()}} Page'),
      ),
    );
    {{/bloc}}
  }
}

{{#bloc}}
class _InitialWidget extends StatelessWidget {
  const _InitialWidget();

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Text('Initial State'),
      ),
    );
  }
}

class _LoadingWidget extends StatelessWidget {
  const _LoadingWidget();

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}

class _ContentWidget extends StatelessWidget {
  final {{entity.pascalCase()}} {{entity.camelCase()}};

  const _ContentWidget({required this.{{entity.camelCase()}}});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Text('{{entity.pascalCase()}}: \${{entity.camelCase()}}.id'),
      ),
    );
  }
}

class _ListContentWidget extends StatelessWidget {
  final List<{{entity.pascalCase()}}> {{entity.camelCase()}}s;

  const _ListContentWidget({required this.{{entity.camelCase()}}s});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ListView.builder(
        itemCount: {{entity.camelCase()}}s.length,
        itemBuilder: (context, index) {
          final {{entity.camelCase()}} = {{entity.camelCase()}}s[index];
          return ListTile(
            title: Text('{{entity.camelCase()}}: \${{entity.camelCase()}}.id'),
          );
        },
      ),
    );
  }
}

class _ErrorWidget extends StatelessWidget {
  final String message;

  const _ErrorWidget({required this.message});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Text('Error: \$message'),
      ),
    );
  }
}
{{/bloc}}