import 'package:fpdart/fpdart.dart';
import '../../../../../core/error/failure.dart';
import '../../../../../core/usecase/usecase.dart';
import '../../domain/entities/{{name.snakeCase}}.dart';
import '../../domain/repositories/{{name.snakeCase}}_repository.dart';

/// Use case for {{name.pascalCase}} operations
/// 
/// This use case handles the business logic for {{name.camelCase}} operations.
/// It follows the Clean Architecture principle by depending only on the 
/// domain layer (repository interface) and not on implementation details.
class {{name.pascalCase}}UseCase implements UseCase<{{name.pascalCase}}, {{name.pascalCase}}Params> {
  final {{name.pascalCase}}Repository repository;

  const {{name.pascalCase}}UseCase(this.repository);

  @override
  Future<Either<Failure, {{name.pascalCase}}>> call({{name.pascalCase}}Params params) async {
    try {
      // Validate input parameters
      if (params.{{name.camelCase}}.id.isEmpty) {
        return Left(ValidationFailure(message: '{{name.pascalCase}} ID cannot be empty'));
      }

      // Execute the repository operation
      // You can customize this based on your specific use case:
      // - For create operations: return await repository.create{{name.pascalCase}}(params.{{name.camelCase}});
      // - For read operations: return await repository.get{{name.pascalCase}}(params.id);
      // - For update operations: return await repository.update{{name.pascalCase}}(params.{{name.camelCase}});
      // - For delete operations: return await repository.delete{{name.pascalCase}}(params.id);
      
      // Example implementation (customize as needed):
      return await repository.get{{name.pascalCase}}(params.{{name.camelCase}}.id);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to process {{name.camelCase}}: ${e.toString()}'));
    }
  }
}

/// Parameters for {{name.pascalCase}}UseCase
/// 
/// Contains all the data needed to execute the use case.
/// Add or modify fields based on your specific requirements.
class {{name.pascalCase}}Params {
  final {{name.pascalCase}} {{name.camelCase}};
  
  /// Additional parameters can be added here:
  /// final String? searchQuery;
  /// final int? limit;
  /// final int? offset;

  const {{name.pascalCase}}Params({
    required this.{{name.camelCase}},
    /// Add additional parameters here
  });
  
  /// Convenience constructor for ID-only operations
  {{name.pascalCase}}Params.withId(String id) : {{name.camelCase}} = {{name.pascalCase}}(id: id, name: '');
}