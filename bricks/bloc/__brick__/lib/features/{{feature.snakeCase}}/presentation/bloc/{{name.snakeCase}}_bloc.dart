import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fpdart/fpdart.dart';
import '../../../../../core/usecase/usecase.dart';
import '../../domain/entities/{{entity.snakeCase}}.dart';
import '{{name.snakeCase}}_event.dart';
import '{{name.snakeCase}}_state.dart';

/// BLoC for managing {{name.pascalCase}} state
/// 
/// This BLoC handles all {{entity.camelCase}}-related operations and state management.
/// It follows the BLoC pattern by accepting events and emitting states.
/// 
/// Usage:
/// ```dart
/// // In your widget:
/// context.read<{{name.pascalCase}}Bloc>().add(Load{{entity.pascalCase}}s());
/// 
/// // Listen to state changes:
/// BlocBuilder<{{name.pascalCase}}Bloc, {{name.pascalCase}}State>(
///   builder: (context, state) {
///     return state.when(
///       initial: () => const SizedBox(),
///       loading: () => const CircularProgressIndicator(),
///       loaded: ({{entity.camelCase}}) => {{entity.pascalCase}}Widget({{entity.camelCase}}: {{entity.camelCase}}),
///       error: (message) => Text('Error: $message'),
///     );
///   },
/// )
/// ```
class {{name.pascalCase}}Bloc extends Bloc<{{name.pascalCase}}Event, {{name.pascalCase}}State> {
  // Use cases for {{entity.camelCase}} operations
  // Uncomment and inject the required use cases:
  // final Create{{entity.pascalCase}}UseCase _create{{entity.pascalCase}}UseCase;
  // final Get{{entity.pascalCase}}UseCase _get{{entity.pascalCase}}UseCase;
  // final GetAll{{entity.pascalCase}}sUseCase _getAll{{entity.pascalCase}}sUseCase;
  // final Update{{entity.pascalCase}}UseCase _update{{entity.pascalCase}}UseCase;
  // final Delete{{entity.pascalCase}}UseCase _delete{{entity.pascalCase}}UseCase;

  {{name.pascalCase}}Bloc({
    // Inject your use cases here:
    // required Get{{entity.pascalCase}}UseCase get{{entity.pascalCase}}UseCase,
    // required GetAll{{entity.pascalCase}}sUseCase getAll{{entity.pascalCase}}sUseCase,
    // required Create{{entity.pascalCase}}UseCase create{{entity.pascalCase}}UseCase,
    // required Update{{entity.pascalCase}}UseCase update{{entity.pascalCase}}UseCase,
    // required Delete{{entity.pascalCase}}UseCase delete{{entity.pascalCase}}UseCase,
  }) : 
    // Initialize use cases:
    // _get{{entity.pascalCase}}UseCase = get{{entity.pascalCase}}UseCase,
    // _getAll{{entity.pascalCase}}sUseCase = getAll{{entity.pascalCase}}sUseCase,
    // _create{{entity.pascalCase}}UseCase = create{{entity.pascalCase}}UseCase,
    // _update{{entity.pascalCase}}UseCase = update{{entity.pascalCase}}UseCase,
    // _delete{{entity.pascalCase}}UseCase = delete{{entity.pascalCase}}UseCase,
    super(const {{name.pascalCase}}State.initial()) {
    
    // Register event handlers
    on<Load{{entity.pascalCase}}s>(_onLoad{{entity.pascalCase}}s);
    on<Load{{entity.pascalCase}}>(_onLoad{{entity.pascalCase}});
    on<Create{{entity.pascalCase}}>(_onCreate{{entity.pascalCase}});
    on<Update{{entity.pascalCase}}>(_onUpdate{{entity.pascalCase}});
    on<Delete{{entity.pascalCase}}>(_onDelete{{entity.pascalCase}});
  }

  /// Handles loading all {{entity.camelCase}}s
  Future<void> _onLoad{{entity.pascalCase}}s(
    Load{{entity.pascalCase}}s event,
    Emitter<{{name.pascalCase}}State> emit,
  ) async {
    emit(const {{name.pascalCase}}State.loading());
    
    // TODO: Uncomment and implement when use case is available
    // final failureOr{{entity.pascalCase}}s = await _getAll{{entity.pascalCase}}sUseCase(const NoParams());
    // emit(
    //   failureOr{{entity.pascalCase}}s.fold(
    //     (failure) => {{name.pascalCase}}State.error(failure.message),
    //     ({{entity.camelCase}}s) => {{name.pascalCase}}State.loadedAll({{entity.camelCase}}s),
    //   ),
    // );
    
    // Temporary mock implementation - remove when implementing real logic
    await Future.delayed(const Duration(seconds: 1));
    emit(const {{name.pascalCase}}State.loadedAll([]));
  }

  /// Handles loading a single {{entity.camelCase}}
  Future<void> _onLoad{{entity.pascalCase}}(
    Load{{entity.pascalCase}} event,
    Emitter<{{name.pascalCase}}State> emit,
  ) async {
    emit(const {{name.pascalCase}}State.loading());
    
    // TODO: Uncomment and implement when use case is available
    // final failureOr{{entity.pascalCase}} = await _get{{entity.pascalCase}}UseCase(
    //   Get{{entity.pascalCase}}Params(id: event.id)
    // );
    // emit(
    //   failureOr{{entity.pascalCase}}.fold(
    //     (failure) => {{name.pascalCase}}State.error(failure.message),
    //     ({{entity.camelCase}}) => {{name.pascalCase}}State.loaded({{entity.camelCase}}),
    //   ),
    // );
    
    // Temporary mock implementation - remove when implementing real logic
    await Future.delayed(const Duration(seconds: 1));
    const mock{{entity.pascalCase}} = {{entity.pascalCase}}(id: 'mock', name: 'Mock {{entity.pascalCase}}');
    emit({{name.pascalCase}}State.loaded(mock{{entity.pascalCase}}));
  }

  /// Handles creating a new {{entity.camelCase}}
  Future<void> _onCreate{{entity.pascalCase}}(
    Create{{entity.pascalCase}} event,
    Emitter<{{name.pascalCase}}State> emit,
  ) async {
    emit(const {{name.pascalCase}}State.loading());
    
    // TODO: Uncomment and implement when use case is available
    // final failureOr{{entity.pascalCase}} = await _create{{entity.pascalCase}}UseCase(
    //   Create{{entity.pascalCase}}Params({{entity.camelCase}}: event.{{entity.camelCase}})
    // );
    // emit(
    //   failureOr{{entity.pascalCase}}.fold(
    //     (failure) => {{name.pascalCase}}State.error(failure.message),
    //     ({{entity.camelCase}}) => {{name.pascalCase}}State.loaded({{entity.camelCase}}),
    //   ),
    // );
    
    // Temporary mock implementation - remove when implementing real logic
    await Future.delayed(const Duration(seconds: 1));
    emit({{name.pascalCase}}State.loaded(event.{{entity.camelCase}}));
  }

  /// Handles updating an existing {{entity.camelCase}}
  Future<void> _onUpdate{{entity.pascalCase}}(
    Update{{entity.pascalCase}} event,
    Emitter<{{name.pascalCase}}State> emit,
  ) async {
    emit(const {{name.pascalCase}}State.loading());
    
    // TODO: Uncomment and implement when use case is available
    // final failureOr{{entity.pascalCase}} = await _update{{entity.pascalCase}}UseCase(
    //   Update{{entity.pascalCase}}Params({{entity.camelCase}}: event.{{entity.camelCase}})
    // );
    // emit(
    //   failureOr{{entity.pascalCase}}.fold(
    //     (failure) => {{name.pascalCase}}State.error(failure.message),
    //     ({{entity.camelCase}}) => {{name.pascalCase}}State.loaded({{entity.camelCase}}),
    //   ),
    // );
    
    // Temporary mock implementation - remove when implementing real logic
    await Future.delayed(const Duration(seconds: 1));
    emit({{name.pascalCase}}State.loaded(event.{{entity.camelCase}}));
  }

  /// Handles deleting a {{entity.camelCase}}
  Future<void> _onDelete{{entity.pascalCase}}(
    Delete{{entity.pascalCase}} event,
    Emitter<{{name.pascalCase}}State> emit,
  ) async {
    emit(const {{name.pascalCase}}State.loading());
    
    // TODO: Uncomment and implement when use case is available
    // final failureOrVoid = await _delete{{entity.pascalCase}}UseCase(
    //   Delete{{entity.pascalCase}}Params(id: event.id)
    // );
    // emit(
    //   failureOrVoid.fold(
    //     (failure) => {{name.pascalCase}}State.error(failure.message),
    //     (_) => const {{name.pascalCase}}State.loadedAll([]), // Refresh the list
    //   ),
    // );
    
    // Temporary mock implementation - remove when implementing real logic
    await Future.delayed(const Duration(seconds: 1));
    emit(const {{name.pascalCase}}State.loadedAll([]));
  }
}