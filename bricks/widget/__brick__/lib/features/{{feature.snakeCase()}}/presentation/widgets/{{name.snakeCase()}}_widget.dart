import 'package:flutter/material.dart';

class {{name.pascalCase()}}Widget extends StatelessWidget {
  final String title;
  final VoidCallback? onPressed;

  const {{name.pascalCase()}}Widget({
    super.key,
    this.title = '{{name.pascalCase()}}',
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: onPressed,
              child: const Text('Click me'),
            ),
          ],
        ),
      ),
    );
  }
}