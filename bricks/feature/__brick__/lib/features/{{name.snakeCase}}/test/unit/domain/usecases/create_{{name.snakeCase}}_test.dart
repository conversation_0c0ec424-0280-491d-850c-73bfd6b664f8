import 'package:flutter_test/flutter_test.dart';
import 'package:fpdart/fpdart.dart';
import 'package:mocktail/mocktail.dart';
import '../../../../../core/error/failure.dart';
import '../../../../../core/usecase/usecase.dart';
import '../../../../domain/entities/{{name.snakeCase}}.dart';
import '../../../../domain/repositories/{{name.snakeCase}}_repository.dart';
import '../../../../domain/usecases/create_{{name.snakeCase}}.dart';

class Mock{{name.pascalCase}}Repository extends Mock implements {{name.pascalCase}}Repository {}

void main() {
  late Create{{name.pascalCase}}UseCase usecase;
  late Mock{{name.pascalCase}}Repository mock{{name.pascalCase}}Repository;

  setUp(() {
    mock{{name.pascalCase}}Repository = Mock{{name.pascalCase}}Repository();
    usecase = Create{{name.pascalCase}}UseCase(mock{{name.pascalCase}}Repository);
  });

  const t{{name.pascalCase}} = {{name.pascalCase}}(
    id: '1',
    name: 'Test {{name.pascalCase}}',
  );

  test('should create a {{name.snakeCase}}', () async {
    // arrange
    when(() => mock{{name.pascalCase}}Repository.create{{name.pascalCase}}(any))
        .thenAnswer((_) async => const Right(t{{name.pascalCase}}));

    // act
    final result = await usecase(const Create{{name.pascalCase}}Params({{name.camelCase}}: t{{name.pascalCase}}));

    // assert
    expect(result, const Right(t{{name.pascalCase}}));
    verify(() => mock{{name.pascalCase}}Repository.create{{name.pascalCase}}(t{{name.pascalCase}})).called(1);
  });

  test('should return a failure when creating a {{name.snakeCase}} fails', () async {
    // arrange
    final failure = ServerFailure(message: 'Server error');
    when(() => mock{{name.pascalCase}}Repository.create{{name.pascalCase}}(any))
        .thenAnswer((_) async => Left(failure));

    // act
    final result = await usecase(const Create{{name.pascalCase}}Params({{name.camelCase}}: t{{name.pascalCase}}));

    // assert
    expect(result, Left(failure));
  });
}