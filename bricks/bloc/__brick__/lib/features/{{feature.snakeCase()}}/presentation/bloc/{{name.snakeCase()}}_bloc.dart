import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/{{entity.snakeCase()}}.dart';
import '../../domain/usecases/get_{{entity.snakeCase()}}_usecase.dart';
import '../../domain/usecases/create_{{entity.snakeCase()}}_usecase.dart';
import '../../domain/usecases/update_{{entity.snakeCase()}}_usecase.dart';
import '../../domain/usecases/delete_{{entity.snakeCase()}}_usecase.dart';

part '{{name.snakeCase()}}_event.dart';
part '{{name.snakeCase()}}_state.dart';
part '{{name.snakeCase()}}_bloc.freezed.dart';

class {{name.pascalCase()}}Bloc extends Bloc<{{name.pascalCase()}}Event, {{name.pascalCase()}}State> {
  final Get{{entity.pascalCase()}}Usecase get{{entity.pascalCase()}}Usecase;
  final Create{{entity.pascalCase()}}Usecase create{{entity.pascalCase()}}Usecase;
  final Update{{entity.pascalCase()}}Usecase update{{entity.pascalCase()}}Usecase;
  final Delete{{entity.pascalCase()}}Usecase delete{{entity.pascalCase()}}Usecase;

  {{name.pascalCase()}}Bloc({
    required this.get{{entity.pascalCase()}}Usecase,
    required this.create{{entity.pascalCase()}}Usecase,
    required this.update{{entity.pascalCase()}}Usecase,
    required this.delete{{entity.pascalCase()}}Usecase,
  }) : super(const {{name.pascalCase()}}State.initial()) {
    on<{{name.pascalCase()}}Event>((event, emit) async {
      await event.when(
        started: () async {
          emit(const {{name.pascalCase()}}State.initial());
        },
        load{{entity.pascalCase()}}: () async {
          emit(const {{name.pascalCase()}}State.loading());
          // TODO: Implement load logic
        },
        create{{entity.pascalCase()}}: ({{entity.camelCase()}}) async {
          emit(const {{name.pascalCase()}}State.loading());
          // TODO: Implement create logic
        },
        update{{entity.pascalCase()}}: ({{entity.camelCase()}}) async {
          emit(const {{name.pascalCase()}}State.loading());
          // TODO: Implement update logic
        },
        delete{{entity.pascalCase()}}: (id) async {
          emit(const {{name.pascalCase()}}State.loading());
          // TODO: Implement delete logic
        },
      );
    });
  }
}