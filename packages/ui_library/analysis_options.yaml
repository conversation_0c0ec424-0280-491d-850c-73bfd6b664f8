# Analysis options for ui_library package
# This package contains shared UI components for Flutter applications

include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"

linter:
  rules:
    # 废弃 API 检测
    - deprecated_member_use_from_same_package

    # Material Design 3 最佳实践
    - use_colored_box
    - use_decorated_box

    # UI 性能优化
    - avoid_unnecessary_containers
    - sized_box_for_whitespace
    - prefer_const_constructors
    - prefer_const_literals_to_create_immutables

    # UI 组件最佳实践
    - prefer_final_fields
    - avoid_redundant_argument_values
