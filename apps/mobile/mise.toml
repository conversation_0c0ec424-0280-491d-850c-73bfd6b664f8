[tools]
flutter = "3.32.5-stable"
java = "23.0.2"

[hooks]

[env]

[tasks.flutter]
description = "run get flutter version"
alias = "version"
run = "flutter --version"

[tasks.flutter-doctor]
description = "run flutter doctor"
alias = "doctor"
run = "flutter doctor -v"

[tasks.pub-get]
description = "run get flutter project pub"
run = "flutter pub get"

[tasks.dev-android]
alias = "dev-and"
description = "run for android"
run = "flutter run --no-sound-null-safety -d M2012K11AC"
depends = ['pub-get']

[tasks.prod-android]
alias = "prod-and"
description = "build apk for android"
run = "flutter build apk --release --flavor production -t lib/main_production.dart"
depends = ['pub-get']
