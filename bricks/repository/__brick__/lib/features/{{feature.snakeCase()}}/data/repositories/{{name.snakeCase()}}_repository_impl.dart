import '../../domain/repositories/{{name.snakeCase()}}_repository.dart';
import '../../domain/entities/{{model.snakeCase()}}.dart';
import '../datasources/{{model.snakeCase()}}_remote_data_source.dart';
import '../datasources/{{model.snakeCase()}}_local_data_source.dart';
import '../models/{{model.snakeCase()}}_model.dart';

class {{name.pascalCase()}}RepositoryImpl implements {{name.pascalCase()}}Repository {
  final {{model.pascalCase()}}RemoteDataSource remoteDataSource;
  final {{model.pascalCase()}}LocalDataSource localDataSource;

  {{name.pascalCase()}}RepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<{{model.pascalCase()}}?> get{{model.pascalCase()}}ById(String id) async {
    // Try to get from local first
    try {
      final localModel = await localDataSource.get{{model.pascalCase()}}(id);
      if (localModel != null) {
        return localModel.toEntity();
      }
    } catch (e) {
      // Handle local data source error
    }

    // Get from remote
    try {
      final remoteModel = await remoteDataSource.get{{model.pascalCase()}}(id);
      // Cache locally
      await localDataSource.save{{model.pascalCase()}}(remoteModel);
      return remoteModel.toEntity();
    } catch (e) {
      // Handle remote data source error
      rethrow;
    }
  }

  @override
  Future<List<{{model.pascalCase()}}>> getAll{{model.pascalCase()}}s() async {
    try {
      final models = await remoteDataSource.getAll{{model.pascalCase()}}s();
      // Update local cache
      await localDataSource.saveAll{{model.pascalCase()}}s(models);
      return models.map((model) => model.toEntity()).toList();
    } catch (e) {
      // Fallback to local data
      final localModels = await localDataSource.getAll{{model.pascalCase()}}s();
      return localModels.map((model) => model.toEntity()).toList();
    }
  }

  @override
  Future<{{model.pascalCase()}}> create{{model.pascalCase()}}({{model.pascalCase()}} {{model.camelCase()}}) async {
    final model = {{model.pascalCase()}}Model.fromEntity({{model.camelCase()}});
    final createdModel = await remoteDataSource.create{{model.pascalCase()}}(model);
    // Save to local cache
    await localDataSource.save{{model.pascalCase()}}(createdModel);
    return createdModel.toEntity();
  }

  @override
  Future<{{model.pascalCase()}}> update{{model.pascalCase()}}({{model.pascalCase()}} {{model.camelCase()}}) async {
    final model = {{model.pascalCase()}}Model.fromEntity({{model.camelCase()}});
    final updatedModel = await remoteDataSource.update{{model.pascalCase()}}(model);
    // Update local cache
    await localDataSource.save{{model.pascalCase()}}(updatedModel);
    return updatedModel.toEntity();
  }

  @override
  Future<void> delete{{model.pascalCase()}}(String id) async {
    await remoteDataSource.delete{{model.pascalCase()}}(id);
    // Remove from local cache
    await localDataSource.delete{{model.pascalCase()}}(id);
  }
}