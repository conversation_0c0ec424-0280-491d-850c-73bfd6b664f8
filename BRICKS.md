# Mason Bricks 使用指南

## 🎯 什么是Bricks？

Bricks是**Mason代码模板**，用于**标准化代码生成**，确保所有功能模块遵循统一的Clean Architecture结构。

## 📦 可用模板清单

| 模板名称 | 命令示例 | 生成内容 | 使用场景 |
|---------|----------|----------|----------|
| **feature** | `mason make feature` | 完整功能模块 | 新建业务功能 |
| **model** | `mason make model` | 数据模型 | 定义数据结构 |
| **repository** | `mason make repository` | 仓储层 | 数据访问接口 |
| **usecase** | `mason make usecase` | 用例类 | 业务逻辑 |
| **bloc** | `mason make bloc` | 状态管理 | BLoC状态管理 |
| **page** | `mason make page` | 页面组件 | 新建页面 |
| **widget** | `mason make widget` | UI组件 | 创建可复用UI组件 |
| **adr** | `mason make adr` | 架构决策记录 | 记录技术决策 |
| **data_source** | `mason make data_source` | 数据源 | 实现数据访问 |
| **api_client** | `mason make api_client` | API客户端 | 网络请求处理 |
| **validator** | `mason make validator` | 验证器 | 输入验证逻辑 |
| **test** | `mason make test` | 测试文件 | 单元/组件测试 |
| **service** | `mason make service` | 服务组件 | 后台/平台服务 |

## 🚀 快速开始

### 1. 安装Mason
```bash
dart pub global activate mason_cli
```

### 2. 初始化
```bash
mason init
```

### 3. 使用模板

#### 🔥 创建完整功能
```bash
# 生成认证功能完整模块
mason make feature --name auth --entity true --repository true --bloc true

# 生成商品功能（简化版）
mason make feature --name product --entity true --bloc false
```

#### 📊 创建数据模型
```bash
# 生成用户数据模型
mason make model --name user

# 生成订单模型（带验证）
mason make model --name order
```

#### 🎨 创建页面
```bash
# 生成登录页面
mason make page --name login

# 生成设置页面（带BLoC）
mason make page --name settings --bloc true
```

#### 🧪 创建测试文件
```bash
# 为usecase创建单元测试
mason make test --name user_usecase --type unit --feature auth

# 为widget创建组件测试
mason make test --name login_page --type widget --feature auth
```

#### ⚙️ 创建服务组件
```bash
# 创建后台服务
mason make service --name notification --type background --feature core

# 创建通知服务
mason make service --name push --type notification --feature core
```

## 📁 生成文件结构

### Feature模板输出
```
lib/features/[name]/
├── data/
│   ├── datasources/
│   ├── models/
│   └── repositories/
├── domain/
│   ├── entities/
│   ├── repositories/
│   ├── usecases/
│   └── validators/
├── presentation/
│   ├── bloc/
│   ├── pages/
│   └── widgets/
└── test/
    ├── unit/
    ├── widget/
    └── integration/
```

### Service模板输出
```
lib/features/[feature]/domain/services/
└── [name]_service.dart
```

### Test模板输出
```
lib/features/[feature]/test/[type]/
└── [name]_test.dart
```

## 🎯 实际工作流

### 场景1：新建用户管理功能
```bash
# 1. 创建完整功能
mason make feature --name user_management

# 2. 添加用户详情页面
mason make page --name user_detail --feature user_management

# 3. 添加用户设置页面
mason make page --name user_settings --feature user_management

# 4. 为功能添加测试
mason make test --name user_management --type unit --feature user_management
```

### 场景2：快速原型开发
```bash
# 先创建简单页面验证想法
mason make page --name prototype

# 确认需求后扩展为完整功能
mason make feature --name confirmed_feature
```

### 场景3：添加网络功能
```bash
# 1. 创建API客户端
mason make api_client --name user --base_url https://api.example.com

# 2. 创建数据源
mason make data_source --name user --type remote --feature user_management

# 3. 添加验证器
mason make validator --name user --feature user_management
```

## 🔧 高级用法

### 自定义变量
所有模板支持智能替换：
- `{{name}}` → 保持原样
- `{{name.pascalCase}}` → UserProfile
- `{{name.snakeCase}}` → user_profile
- `{{name.camelCase}}` → userProfile

### 批量生成
```bash
# 创建多个相关文件
mason make feature --name order
mason make model --name order_item --feature order
mason make repository --name order --feature order
mason make usecase --name create_order --feature order
```

## 🛠️ 开发工作流

1. **需求分析** → 确定需要的新功能
2. **模板选择** → 选择合适brick模板
3. **代码生成** → 执行mason命令
4. **业务实现** → 填充业务逻辑
5. **测试验证** → 确保质量

## 📋 常用命令速查

```bash
# 查看所有模板
mason list

# 查看模板详情
mason bundle bricks/feature

# 生成并预览
mason make feature --name test --dry-run
```