import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/usecase/usecase.dart';
import '../../../../core/router/auth_guard.dart';

import '../../domain/entities/auth_user.dart';
import '../../domain/usecases/get_current_user_usecase.dart';
import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../domain/usecases/sms_login_usecase.dart';
import '../../domain/usecases/send_sms_code_usecase.dart';
import '../../domain/usecases/wechat_login_usecase.dart';

part 'auth_event.dart';
part 'auth_state.dart';

@injectable
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final LoginUseCase loginUseCase;
  final LogoutUseCase logoutUseCase;
  final GetCurrentUserUseCase getCurrentUserUseCase;
  final SmsLoginUseCase smsLoginUseCase;
  final SendSmsCodeUseCase sendSmsCodeUseCase;
  final WechatLoginUseCase wechatLoginUseCase;

  AuthBloc({
    required this.loginUseCase,
    required this.logoutUseCase,
    required this.getCurrentUserUseCase,
    required this.smsLoginUseCase,
    required this.sendSmsCodeUseCase,
    required this.wechatLoginUseCase,
  }) : super(const AuthState()) {
    on<AuthStarted>(_onAuthStarted);
    on<AuthLoginRequested>(_onAuthLoginRequested);
    on<AuthSmsLoginRequested>(_onAuthSmsLoginRequested);
    on<AuthSendSmsCodeRequested>(_onAuthSendSmsCodeRequested);
    on<AuthWechatLoginRequested>(_onAuthWechatLoginRequested);
    on<AuthLogoutRequested>(_onAuthLogoutRequested);
    on<AuthUserUpdated>(_onAuthUserUpdated);
  }

  Future<void> _onAuthStarted(
    AuthStarted event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));

    final result = await getCurrentUserUseCase(const NoParams());
    result.fold(
      (failure) => emit(state.copyWith(
        status: AuthStatus.unauthenticated,
        error: failure.message,
      )),
      (user) {
        if (user != null) {
          AuthGuard.setAuthenticated(true);
          emit(state.copyWith(
            status: AuthStatus.authenticated,
            user: user,
          ));
        } else {
          AuthGuard.setAuthenticated(false);
          emit(state.copyWith(status: AuthStatus.unauthenticated));
        }
      },
    );
  }

  Future<void> _onAuthLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));

    final result = await loginUseCase(LoginParams(
      email: event.email,
      password: event.password,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        status: AuthStatus.unauthenticated,
        error: failure.message,
      )),
      (user) {
        AuthGuard.setAuthenticated(true);
        emit(state.copyWith(
          status: AuthStatus.authenticated,
          user: user,
          error: null,
        ));
      },
    );
  }

  Future<void> _onAuthSmsLoginRequested(
    AuthSmsLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));

    final result = await smsLoginUseCase(SmsLoginParams(
      phone: event.phone,
      code: event.code,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        status: AuthStatus.unauthenticated,
        error: failure.message,
      )),
      (user) {
        AuthGuard.setAuthenticated(true);
        emit(state.copyWith(
          status: AuthStatus.authenticated,
          user: user,
          error: null,
        ));
      },
    );
  }

  Future<void> _onAuthSendSmsCodeRequested(
    AuthSendSmsCodeRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));

    final result = await sendSmsCodeUseCase(SendSmsCodeParams(
      phone: event.phone,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        status: AuthStatus.unauthenticated,
        error: failure.message,
      )),
      (_) => emit(state.copyWith(
        status: AuthStatus.codeSent,
        error: null,
      )),
    );
  }

  Future<void> _onAuthWechatLoginRequested(
    AuthWechatLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.wechatLoading));

    final result = await wechatLoginUseCase(const NoParams());

    result.fold(
      (failure) => emit(state.copyWith(
        status: AuthStatus.unauthenticated,
        error: failure.message,
      )),
      (user) {
        AuthGuard.setAuthenticated(true);
        emit(state.copyWith(
          status: AuthStatus.authenticated,
          user: user,
          error: null,
        ));
      },
    );
  }

  Future<void> _onAuthLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));

    final result = await logoutUseCase(const NoParams());
    result.fold(
      (failure) => emit(state.copyWith(
        status: AuthStatus.authenticated,
        error: failure.message,
      )),
      (_) {
        AuthGuard.setAuthenticated(false);
        emit(state.copyWith(
          status: AuthStatus.unauthenticated,
          user: null,
          error: null,
        ));
      },
    );
  }

  void _onAuthUserUpdated(
    AuthUserUpdated event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(
      user: event.user,
    ));
  }
}