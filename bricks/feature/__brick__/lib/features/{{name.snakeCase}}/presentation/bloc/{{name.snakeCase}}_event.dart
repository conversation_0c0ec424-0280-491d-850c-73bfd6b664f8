import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/{{name.snakeCase}}.dart';

part '{{name.snakeCase}}_event.freezed.dart';

@freezed
class {{name.pascalCase}}Event with _${{name.pascalCase}}Event {
  const factory {{name.pascalCase}}Event.create({{name.pascalCase}} {{name.camelCase}}) = Create{{name.pascalCase}};
  
  const factory {{name.pascalCase}}Event.get(String id) = Get{{name.pascalCase}};
  
  const factory {{name.pascalCase}}Event.getAll() = GetAll{{name.pascalCase}}s;
  
  const factory {{name.pascalCase}}Event.update({{name.pascalCase}} {{name.camelCase}}) = Update{{name.pascalCase}};
  
  const factory {{name.pascalCase}}Event.delete(String id) = Delete{{name.pascalCase}};
}