name: feature
description: Creates a new feature module with Clean Architecture structure

vars:
  name:
    type: string
    description: Feature name
    default: my_feature
    prompt: What is the name of the feature?

  entity:
    type: boolean
    description: Include entity?
    default: true

  repository:
    type: boolean
    description: Include repository?
    default: true

  usecase:
    type: boolean
    description: Include usecase?
    default: true

  bloc:
    type: boolean
    description: Include bloc?
    default: true

  page:
    type: boolean
    description: Include page?
    default: true

  tests:
    type: boolean
    description: Include tests?
    default: true