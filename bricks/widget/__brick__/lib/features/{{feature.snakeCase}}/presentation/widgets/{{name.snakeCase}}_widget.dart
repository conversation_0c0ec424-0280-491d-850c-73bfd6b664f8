import 'package:flutter/material.dart';
import '../../domain/entities/{{name.snakeCase}}.dart';

class {{name.pascalCase}}Widget extends StatelessWidget {
  final {{name.pascalCase}} {{name.camelCase}};

  const {{name.pascalCase}}Widget({super.key, required this.{{name.camelCase}}});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              {{name.camelCase}}.name,
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8.0),
            Text(
              {{name.camelCase}}.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8.0),
            Text(
              {{name.camelCase}}.id,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }
}