import 'package:data_models/data_models.dart' as dm;
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../domain/entities/auth_user.dart';

part 'auth_user_model.freezed.dart';
part 'auth_user_model.g.dart';

@freezed
abstract class AuthUserModel with _$AuthUserModel {
  const factory AuthUserModel({
    required String id,
    required String email,
    String? name,
    String? avatar,
    String? accessToken,
    String? refreshToken,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _AuthUserModel;

  const AuthUserModel._();

  factory AuthUserModel.fromJson(Map<String, dynamic> json) =>
      _$AuthUserModelFromJson(json);

  /// Convert to domain entity (which is now just User)
  AuthUser toEntity() => dm.User(
        id: id,
        email: email,
        name: name,
        avatar: avatar,
        createdAt: createdAt,
        updatedAt: updatedAt,
        accessToken: accessToken,
        refreshToken: refreshToken,
      );

  /// Create from domain entity
  factory AuthUserModel.fromEntity(AuthUser entity) => AuthUserModel(
        id: entity.id,
        email: entity.email,
        name: entity.name,
        avatar: entity.avatar,
        accessToken: entity.accessToken,
        refreshToken: entity.refreshToken,
        createdAt: entity.createdAt,
        updatedAt: entity.updatedAt,
      );
}