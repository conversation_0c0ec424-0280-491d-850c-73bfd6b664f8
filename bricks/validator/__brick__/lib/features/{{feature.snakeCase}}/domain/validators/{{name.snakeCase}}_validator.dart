class {{name.pascalCase}}Validator {
  const {{name.pascalCase}}Validator._();

  static String? validate{{name.pascalCase}}(String? value) {
    if (value == null || value.isEmpty) {
      return '{{name.pascalCase}} is required';
    }
    
    // TODO: Add custom validation logic
    // Example validations:
    // if (value.length < 3) {
    //   return '{{name.pascalCase}} must be at least 3 characters long';
    // }
    //
    // if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value)) {
    //   return '{{name.pascalCase}} can only contain letters, numbers, and underscores';
    // }
    
    return null;
  }
}