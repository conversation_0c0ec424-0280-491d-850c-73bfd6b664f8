abstract class {{name.pascalCase()}}Repository {
  Future<{{model.pascalCase()}}?> get{{model.pascalCase()}}ById(String id);
  Future<List<{{model.pascalCase()}}>> getAll{{model.pascalCase()}}s();
  Future<{{model.pascalCase()}}> create{{model.pascalCase()}}({{model.pascalCase()}} {{model.camelCase()}});
  Future<{{model.pascalCase()}}> update{{model.pascalCase()}}({{model.pascalCase()}} {{model.camelCase()}});
  Future<void> delete{{model.pascalCase()}}(String id);
}