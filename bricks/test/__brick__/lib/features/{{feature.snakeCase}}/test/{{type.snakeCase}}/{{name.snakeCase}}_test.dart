import 'package:flutter_test/flutter_test.dart';
{{#isUnit}}
import 'package:mocktail/mocktail.dart';
{{/isUnit}}
{{#isWidget}}
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
{{/isWidget}}

{{#isUnit}}
// TODO: Import the component to test
// import '../../../../domain/usecases/{{name.snakeCase}}.dart';
// import '../../../../domain/repositories/{{name.snakeCase}}_repository.dart';

{{#isUseCase}}
class Mock{{name.pascalCase}}Repository extends Mock implements {{name.pascalCase}}Repository {}

void main() {
  // late {{name.pascalCase}}UseCase usecase;
  // late Mock{{name.pascalCase}}Repository mock{{name.pascalCase}}Repository;

  setUp(() {
    // mock{{name.pascalCase}}Repository = Mock{{name.pascalCase}}Repository();
    // usecase = {{name.pascalCase}}UseCase(mock{{name.pascalCase}}Repository);
  });

  test('{{name.pascalCase}}UseCase should ...', () async {
    // TODO: Implement test
    // arrange
    // act
    // assert
  });
}
{{/isUseCase}}

{{#isRepository}}
void main() {
  // TODO: Implement repository tests
  test('{{name.pascalCase}}Repository should ...', () async {
    // TODO: Implement test
  });
}
{{/isRepository}}
{{/isUnit}}

{{#isWidget}}
{{#usesBloc}}
class Mock{{name.pascalCase}}Bloc extends Mock implements {{name.pascalCase}}Bloc {}

void main() {
  // late Mock{{name.pascalCase}}Bloc mock{{name.pascalCase}}Bloc;

  setUp(() {
    // mock{{name.pascalCase}}Bloc = Mock{{name.pascalCase}}Bloc();
  });

  testWidgets('{{name.pascalCase}} widget should ...', (tester) async {
    // TODO: Implement widget test
    // await tester.pumpWidget(
    //   MaterialApp(
    //     home: BlocProvider.value(
    //       value: mock{{name.pascalCase}}Bloc,
    //       child: {{name.pascalCase}}(),
    //     ),
    //   ),
    // );
  });
}
{{/usesBloc}}
{{^usesBloc}}
void main() {
  testWidgets('{{name.pascalCase}} widget should ...', (tester) async {
    // TODO: Implement widget test
    // await tester.pumpWidget(
    //   const MaterialApp(
    //     home: {{name.pascalCase}}(),
    //   ),
    // );
  });
}
{{/usesBloc}}
{{/isWidget}}

{{#isIntegration}}
void main() {
  test('{{name.pascalCase}} integration test should ...', () async {
    // TODO: Implement integration test
  });
}
{{/isIntegration}}