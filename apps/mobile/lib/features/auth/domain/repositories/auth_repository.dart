import 'package:fpdart/fpdart.dart';

import '../../../../core/error/failure.dart';
import '../entities/auth_user.dart';

abstract class AuthRepository {
  Future<Either<Failure, AuthUser>> login(String email, String password);
  Future<Either<Failure, AuthUser>> smsLogin({required String phone, required String code});
  Future<Either<Failure, void>> sendSmsCode({required String phone});
  Future<Either<Failure, AuthUser>> wechatLogin();
  Future<Either<Failure, void>> logout();
  Future<Either<Failure, AuthUser?>> getCurrentUser();
  Future<Either<Failure, void>> refreshToken();
  Future<Either<Failure, void>> register(String email, String password, String name);
  Future<Either<Failure, void>> forgotPassword(String email);
  Future<Either<Failure, bool>> isAuthenticated();
}