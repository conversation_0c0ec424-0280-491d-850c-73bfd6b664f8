import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fpdart/fpdart.dart';
import '../../../../core/usecase/usecase.dart';
import '../../domain/entities/{{name.snakeCase}}.dart';
import '../../domain/usecases/create_{{name.snakeCase}}.dart';
import '../../domain/usecases/delete_{{name.snakeCase}}.dart';
import '../../domain/usecases/get_all_{{name.snakeCase}}s.dart';
import '../../domain/usecases/get_{{name.snakeCase}}.dart';
import '../../domain/usecases/update_{{name.snakeCase}}.dart';
import '{{name.snakeCase}}_event.dart';
import '{{name.snakeCase}}_state.dart';

class {{name.pascalCase}}Bloc extends Bloc<{{name.pascalCase}}Event, {{name.pascalCase}}State> {
  final Create{{name.pascalCase}}UseCase create{{name.pascalCase}}UseCase;
  final Get{{name.pascalCase}}UseCase get{{name.pascalCase}}UseCase;
  final GetAll{{name.pascalCase}}sUseCase getAll{{name.pascalCase}}sUseCase;
  final Update{{name.pascalCase}}UseCase update{{name.pascalCase}}UseCase;
  final Delete{{name.pascalCase}}UseCase delete{{name.pascalCase}}UseCase;

  {{name.pascalCase}}Bloc({
    required this.create{{name.pascalCase}}UseCase,
    required this.get{{name.pascalCase}}UseCase,
    required this.getAll{{name.pascalCase}}sUseCase,
    required this.update{{name.pascalCase}}UseCase,
    required this.delete{{name.pascalCase}}UseCase,
  }) : super(const {{name.pascalCase}}State.initial()) {
    on<Create{{name.pascalCase}}>((event, emit) async {
      emit(const {{name.pascalCase}}State.loading());
      final failureOr{{name.pascalCase}} = await create{{name.pascalCase}}UseCase(
        Create{{name.pascalCase}}Params({{name.camelCase}}: event.{{name.camelCase}})
      );
      emit(
        failureOr{{name.pascalCase}}.fold(
          (failure) => {{name.pascalCase}}State.error(failure.message),
          ({{name.camelCase}}) => {{name.pascalCase}}State.loaded({{name.camelCase}}),
        ),
      );
    });

    on<Get{{name.pascalCase}}>((event, emit) async {
      emit(const {{name.pascalCase}}State.loading());
      final failureOr{{name.pascalCase}} = await get{{name.pascalCase}}UseCase(
        Get{{name.pascalCase}}Params(id: event.id)
      );
      emit(
        failureOr{{name.pascalCase}}.fold(
          (failure) => {{name.pascalCase}}State.error(failure.message),
          ({{name.camelCase}}) => {{name.pascalCase}}State.loaded({{name.camelCase}}),
        ),
      );
    });

    on<GetAll{{name.pascalCase}}s>((event, emit) async {
      emit(const {{name.pascalCase}}State.loading());
      final failureOr{{name.pascalCase}}s = await getAll{{name.pascalCase}}sUseCase(const NoParams());
      emit(
        failureOr{{name.pascalCase}}s.fold(
          (failure) => {{name.pascalCase}}State.error(failure.message),
          ({{name.camelCase}}s) => {{name.pascalCase}}State.loadedAll({{name.camelCase}}s),
        ),
      );
    });

    on<Update{{name.pascalCase}}>((event, emit) async {
      emit(const {{name.pascalCase}}State.loading());
      final failureOr{{name.pascalCase}} = await update{{name.pascalCase}}UseCase(
        Update{{name.pascalCase}}Params({{name.camelCase}}: event.{{name.camelCase}})
      );
      emit(
        failureOr{{name.pascalCase}}.fold(
          (failure) => {{name.pascalCase}}State.error(failure.message),
          ({{name.camelCase}}) => {{name.pascalCase}}State.loaded({{name.camelCase}}),
        ),
      );
    });

    on<Delete{{name.pascalCase}}>((event, emit) async {
      emit(const {{name.pascalCase}}State.loading());
      final failureOrVoid = await delete{{name.pascalCase}}UseCase(
        Delete{{name.pascalCase}}Params(id: event.id)
      );
      emit(
        failureOrVoid.fold(
          (failure) => {{name.pascalCase}}State.error(failure.message),
          (_) => const {{name.pascalCase}}State.loadedAll([]), // Simplified
        ),
      );
    });
  }
}