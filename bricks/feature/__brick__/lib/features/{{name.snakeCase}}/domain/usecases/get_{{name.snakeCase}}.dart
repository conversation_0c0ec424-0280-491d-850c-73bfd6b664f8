import 'package:fpdart/fpdart.dart';
import '../../../../../core/error/failure.dart';
import '../../../../../core/usecase/usecase.dart';
import '../../domain/entities/{{name.snakeCase}}.dart';
import '../../domain/repositories/{{name.snakeCase}}_repository.dart';

class Get{{name.pascalCase}}UseCase implements UseCase<{{name.pascalCase}}, Get{{name.pascalCase}}Params> {
  final {{name.pascalCase}}Repository repository;

  const Get{{name.pascalCase}}UseCase(this.repository);

  @override
  Future<Either<Failure, {{name.pascalCase}}>> call(Get{{name.pascalCase}}Params params) async {
    return await repository.get{{name.pascalCase}}(params.id);
  }
}

class Get{{name.pascalCase}}Params {
  final String id;

  Get{{name.pascalCase}}Params({required this.id});
}