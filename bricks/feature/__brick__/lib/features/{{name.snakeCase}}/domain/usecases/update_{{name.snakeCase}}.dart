import 'package:fpdart/fpdart.dart';
import '../../../../../core/error/failure.dart';
import '../../../../../core/usecase/usecase.dart';
import '../../domain/entities/{{name.snakeCase}}.dart';
import '../../domain/repositories/{{name.snakeCase}}_repository.dart';

class Update{{name.pascalCase}}UseCase implements UseCase<{{name.pascalCase}}, Update{{name.pascalCase}}Params> {
  final {{name.pascalCase}}Repository repository;

  const Update{{name.pascalCase}}UseCase(this.repository);

  @override
  Future<Either<Failure, {{name.pascalCase}}>> call(Update{{name.pascalCase}}Params params) async {
    return await repository.update{{name.pascalCase}}(params.{{name.camelCase}});
  }
}

class Update{{name.pascalCase}}Params {
  final {{name.pascalCase}} {{name.camelCase}};

  Update{{name.pascalCase}}Params({required this.{{name.camelCase}});
}