name: usecase
description: Creates a new use case class
version: 0.1.0

vars:
  name:
    type: string
    description: Use case name
    default: example
    prompt: What is the name of the use case?
  
  feature:
    type: string
    description: Feature name
    default: core
    prompt: What feature does this use case belong to?
  
  repository:
    type: string
    description: Repository name
    default: ""
    prompt: What repository does this use case use?
  
  entity:
    type: string
    description: Entity name
    default: ""
    prompt: What entity does this use case work with?