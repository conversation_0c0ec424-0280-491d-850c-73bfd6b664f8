import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/{{name.snakeCase}}_bloc.dart';
import '../widgets/{{name.snakeCase}}_widget.dart';

class {{name.pascalCase}}Page extends StatelessWidget {
  const {{name.pascalCase}}Page({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('{{name.pascalCase}}'),
      ),
      body: BlocBuilder<{{name.pascalCase}}Bloc, {{name.pascalCase}}State>(
        builder: (context, state) {
          return state.when(
            initial: () => const SizedBox(),
            loading: () => const Center(child: CircularProgressIndicator()),
            loaded: ({{name.camelCase}}) => {{name.pascalCase}}Widget({{name.camelCase}}: {{name.camelCase}}),
            loadedAll: ({{name.camelCase}}s) => ListView.builder(
              itemCount: {{name.camelCase}}s.length,
              itemBuilder: (context, index) {
                return {{name.pascalCase}}Widget({{name.camelCase}}: {{name.camelCase}}s[index]);
              },
            ),
            error: (message) => Center(child: Text(message)),
          );
        },
      ),
    );
  }
}