import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../features/auth/domain/usecases/get_current_user_usecase.dart';
import '../usecase/usecase.dart';
import '../di/di.dart';
import '../logging/app_logger.dart';

class AuthGuard {
  static bool _isAuthenticated = false;
  static const List<String> _publicRoutes = ['/login', '/register', '/forgot-password'];

  static bool get isAuthenticated => _isAuthenticated;

  static Future<void> initialize() async {
    appLogger.auth('Starting AuthGuard initialization');

    final getCurrentUser = getIt<GetCurrentUserUseCase>();
    final result = await getCurrentUser(const NoParams());

    result.fold(
      (failure) {
        appLogger.auth('Failed to get user', success: false, error: failure.message);
        _isAuthenticated = false;
      },
      (user) {
        if (user != null) {
          appLogger.auth('User found during initialization', success: true, userId: user.name);
          _isAuthenticated = true;
        } else {
          appLogger.auth('No user found during initialization', success: false);
          _isAuthenticated = false;
        }
      },
    );

    appLogger.auth('AuthGuard initialization completed', success: _isAuthenticated);
  }

  static void setAuthenticated(bool value) {
    appLogger.auth('Setting authentication status', success: value);
    _isAuthenticated = value;
    // 当认证状态变化时，通过全局导航器进行路由
    if (_isAuthenticated) {
      // 使用延迟确保状态已更新
      Future.delayed(Duration.zero, () {
        final context = navigatorKey.currentContext;
        if (context != null && context.mounted) {
          appLogger.ui('Navigating to home after authentication', screen: '/home');
          context.go('/home');
        }
      });
    }
  }

  // 全局导航器key，需要在main.dart中设置
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  static String? redirect(BuildContext context, GoRouterState state) {
    final isPublicRoute = _publicRoutes.contains(state.fullPath);
    appLogger.ui('Route redirect check',
        screen: state.fullPath,
        data: {'isPublic': isPublicRoute, 'isAuth': _isAuthenticated});

    // If user is not authenticated and trying to access private route
    if (!_isAuthenticated && !isPublicRoute) {
      appLogger.ui('Redirecting to login', screen: '/login',
          data: {'reason': 'unauthenticated user accessing private route'});
      return '/login';
    }

    // If user is authenticated and trying to access auth pages
    if (_isAuthenticated && isPublicRoute) {
      appLogger.ui('Redirecting to home', screen: '/home',
          data: {'reason': 'authenticated user on public route'});
      return '/home';
    }

    appLogger.debug('No redirect needed for route: ${state.fullPath}');
    return null; // No redirect needed
  }
}