import 'package:fpdart/fpdart.dart';
import '../../../../../core/error/failure.dart';
import '../../../../../core/usecase/usecase.dart';
import '../../domain/entities/{{name.snakeCase}}.dart';
import '../../domain/repositories/{{name.snakeCase}}_repository.dart';

class GetAll{{name.pascalCase}}sUseCase implements UseCase<List<{{name.pascalCase}}>, NoParams> {
  final {{name.pascalCase}}Repository repository;

  const GetAll{{name.pascalCase}}sUseCase(this.repository);

  @override
  Future<Either<Failure, List<{{name.pascalCase}}>>> call(NoParams params) async {
    return await repository.getAll{{name.pascalCase}}s();
  }
}