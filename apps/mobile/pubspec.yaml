name: flutter_scaffold_mobile
description: A modern Flutter scaffold application
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1
  flutter: ">=3.32.0"

dependencies:
  flutter:
    sdk: flutter

  # Local Packages
  data_models:
    path: ../../packages/data_models
  ui_library:
    path: ../../packages/ui_library

  # Core
  cupertino_icons: ^1.0.8
  flutter_bloc: ^9.1.1
  bloc: ^9.0.0
  equatable: ^2.0.5
  get_it: ^8.1.0
  injectable: ^2.5.1
  go_router: ^14.2.7

  # Network
  dio: ^5.3.3
  retrofit: ^4.7.0
  pretty_dio_logger: ^1.3.1
  dio_cache_interceptor: ^4.0.3
  connectivity_plus: ^6.1.4

  # Storage
  drift: ^2.28.1
  path_provider: ^2.1.1
  path: ^1.8.3
  shared_preferences: ^2.2.2

  # UI & Theme
  flex_color_scheme: ^8.0.2
  flutter_svg: ^2.0.9

  # Utilities
  fpdart: ^1.1.0
  freezed_annotation: ^3.1.0
  json_annotation: ^4.9.0
  logger: ^2.0.2+1

  # Device Info
  device_info_plus: ^11.5.0
  package_info_plus: ^8.3.0

  # Internationalization
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2
  slang: ^4.7.3
  slang_flutter: ^4.7.0

dependency_overrides:
  intl: ^0.20.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Testing
  mocktail: ^1.0.1
  bloc_test: ^10.0.0
  golden_toolkit: ^0.15.0
  patrol: ^3.18.0

  # Code Generation
  build_runner: ^2.6.0
  freezed: ^3.2.0
  json_serializable: ^6.10.0
  retrofit_generator: ^10.0.1
  injectable_generator: ^2.8.0
  drift_dev: ^2.28.1

  # Assets
  flutter_gen_runner: ^5.11.0
  flutter_launcher_icons: ^0.14.4

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/lottie/
    - assets/fonts/

  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700

flutter_gen:
  output: lib/generated/
  line_length: 80

flutter_launcher_icons:
  android: true
  ios: true
  image_path: assets/icons/app_icon.png
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: assets/icons/app_icon_foreground.png

flutter_intl:
  enabled: true