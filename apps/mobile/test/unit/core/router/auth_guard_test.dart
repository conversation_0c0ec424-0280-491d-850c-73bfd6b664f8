import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';

import 'package:flutter_scaffold_mobile/core/router/auth_guard.dart';

class MockGoRouterState extends Mock implements GoRouterState {}
class MockBuildContext extends Mock implements BuildContext {}

void main() {
  group('AuthGuard', () {
    late MockGoRouterState mockState;
    late MockBuildContext mockContext;

    setUp(() {
      mockState = MockGoRouterState();
      mockContext = MockBuildContext();

      // Reset authentication state
      AuthGuard.setAuthenticated(false);
    });

    test('should redirect to login when not authenticated and accessing private route', () {
      // Arrange
      when(() => mockState.fullPath).thenReturn('/home');
      AuthGuard.setAuthenticated(false);

      // Act
      final result = AuthGuard.redirect(mockContext, mockState);

      // Assert
      expect(result, '/login');
    });

    test('should redirect to home when authenticated and accessing login', () {
      // Arrange
      when(() => mockState.fullPath).thenReturn('/login');
      AuthGuard.setAuthenticated(true);

      // Act
      final result = AuthGuard.redirect(mockContext, mockState);

      // Assert
      expect(result, '/home');
    });

    test('should not redirect when accessing appropriate routes', () {
      // Arrange - authenticated user accessing private route
      when(() => mockState.fullPath).thenReturn('/home');
      AuthGuard.setAuthenticated(true);

      // Act
      final result = AuthGuard.redirect(mockContext, mockState);

      // Assert
      expect(result, null);
    });

    test('should not redirect when unauthenticated user accessing public route', () {
      // Arrange
      when(() => mockState.fullPath).thenReturn('/login');
      AuthGuard.setAuthenticated(false);

      // Act
      final result = AuthGuard.redirect(mockContext, mockState);

      // Assert
      expect(result, null);
    });
  });
}