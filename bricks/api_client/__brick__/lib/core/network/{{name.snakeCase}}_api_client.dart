import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part '{{name.snakeCase}}_api_client.g.dart';

@RestApi(baseUrl: '{{base_url}}')
abstract class {{name.pascalCase}}ApiClient {
  factory {{name.pascalCase}}ApiClient(Dio dio, {String baseUrl}) = _{{name.pascalCase}}ApiClient;

  // TODO: Add API endpoints
  // @GET('/{{name.snakeCase}}s')
  // Future<List<{{name.pascalCase}}Model>> get{{name.pascalCase}}s();
  //
  // @GET('/{{name.snakeCase}}s/{id}')
  // Future<{{name.pascalCase}}Model> get{{name.pascalCase}}(@Path('id') String id);
  //
  // @POST('/{{name.snakeCase}}s')
  // Future<{{name.pascalCase}}Model> create{{name.pascalCase}}(@Body() {{name.pascalCase}}Model {{name.camelCase}});
  //
  // @PUT('/{{name.snakeCase}}s/{id}')
  // Future<{{name.pascalCase}}Model> update{{name.pascalCase}}(@Path('id') String id, @Body() {{name.pascalCase}}Model {{name.camelCase}});
  //
  // @DELETE('/{{name.snakeCase}}s/{id}')
  // Future<void> delete{{name.pascalCase}}(@Path('id') String id);
}