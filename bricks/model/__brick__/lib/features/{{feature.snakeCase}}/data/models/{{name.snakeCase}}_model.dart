import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/{{name.snakeCase}}.dart';

part '{{name.snakeCase}}_model.freezed.dart';
part '{{name.snakeCase}}_model.g.dart';

@freezed
abstract class {{name.pascalCase}}Model with _${{name.pascalCase}}Model {
  const {{name.pascalCase}}Model._();

  const factory {{name.pascalCase}}Model({
    required String id,
    required String name,
    required String description,
  }) = _{{name.pascalCase}}Model;

  factory {{name.pascalCase}}Model.fromJson(Map<String, dynamic> json) =>
      _${{name.pascalCase}}ModelFromJson(json);

  {{name.pascalCase}} toEntity() {
    return {{name.pascalCase}}(
      id: id,
      name: name,
      description: description,
    );
  }

  factory {{name.pascalCase}}Model.fromEntity({{name.pascalCase}} entity) {
    return {{name.pascalCase}}Model(
      id: entity.id,
      name: entity.name,
      description: entity.description,
    );
  }
}