# Flutter Scaffold - Mason 代码模板使用指南

## 🧱 什么是 Mason？

Mason 是一个强大的 Dart 代码生成工具，可以通过预定义的模板快速生成代码。本项目提供了 14 种专业模板，支持 Clean Architecture 的完整开发流程。

## 📋 模板总览

| 模板名称 | 用途 | 生成内容 | 命令示例 |
|---------|------|----------|----------|
| **feature** | 完整功能模块 | 三层架构完整代码 | `mason make feature` |
| **model** | 数据模型 | Entity + Model + 测试 | `mason make model` |
| **repository** | 仓储层 | 接口 + 实现类 | `mason make repository` |
| **usecase** | 用例类 | 业务逻辑封装 | `mason make usecase` |
| **bloc** | 状态管理 | BLoC + Event + State | `mason make bloc` |
| **page** | 页面组件 | UI 页面 + 测试 | `mason make page` |
| **widget** | 可复用组件 | UI 组件 | `mason make widget` |
| **adr** | 架构决策记录 | 技术决策文档 | `mason make adr` |
| **api_client** | API 客户端 | 外部 API 集成 | `mason make api_client` |
| **validator** | 数据验证器 | 输入验证逻辑 | `mason make validator` |
| **service** | 服务类 | 业务服务 | `mason make service` |
| **data_source** | 数据源 | 本地/远程数据源 | `mason make data_source` |
| **test** | 测试文件 | 单元/集成测试 | `mason make test` |

## 🚀 核心模板详解

### 1. Feature 模板（最常用）

创建完整的 Clean Architecture 功能模块。

#### 基本用法
```bash
# 创建完整功能模块
mason make feature --name user_management

# 交互式创建（推荐）
mason make feature
```

#### 高级用法
```bash
# 创建包含所有组件的功能
mason make feature --name user_management \
  --entity true \
  --repository true \
  --usecase true \
  --bloc true \
  --page true \
  --tests true
```

#### 生成的文件结构
```
lib/features/user_management/
├── data/
│   ├── datasources/
│   │   ├── user_management_local_data_source.dart
│   │   └── user_management_remote_data_source.dart
│   ├── models/
│   │   └── user_management_model.dart
│   └── repositories/
│       └── user_management_repository_impl.dart
├── domain/
│   ├── entities/
│   │   └── user_management.dart
│   ├── repositories/
│   │   └── user_management_repository.dart
│   └── usecases/
│       ├── create_user_management.dart
│       ├── delete_user_management.dart
│       ├── get_all_user_managements.dart
│       ├── get_user_management.dart
│       └── update_user_management.dart
├── presentation/
│   ├── bloc/
│   │   ├── user_management_bloc.dart
│   │   ├── user_management_event.dart
│   │   └── user_management_state.dart
│   ├── pages/
│   │   └── user_management_page.dart
│   └── widgets/
│       └── user_management_widget.dart
└── test/
    ├── unit/
    │   └── domain/
    │       └── usecases/
    │           └── create_user_management_test.dart
    └── widget/
        └── presentation/
            └── pages/
                └── user_management_page_test.dart
```

### 2. Model 模板

创建数据模型和实体。

#### 基本用法
```bash
# 创建数据模型
mason make model --name user

# 指定功能模块
mason make model --name user --feature auth
```

#### 生成的文件结构
```
lib/features/auth/data/models/user_model.dart
lib/features/auth/domain/entities/user.dart
```

### 3. Repository 模板

创建仓储模式实现。

#### 基本用法
```bash
# 创建仓储
mason make repository --name user_repository

# 指定功能模块
mason make repository --name user_repository --feature auth
```

#### 生成的文件结构
```
lib/features/auth/data/repositories/user_repository_impl.dart
lib/features/auth/domain/repositories/user_repository.dart
```

### 4. BLoC 模板

创建状态管理代码。

#### 基本用法
```bash
# 创建 BLoC
mason make bloc --name auth

# 指定功能模块
mason make bloc --name auth --feature auth
```

#### 生成的文件结构
```
lib/features/auth/presentation/bloc/auth_bloc.dart
lib/features/auth/presentation/bloc/auth_event.dart
lib/features/auth/presentation/bloc/auth_state.dart
```

### 5. Page 模板

创建页面组件。

#### 基本用法
```bash
# 创建页面
mason make page --name login

# 指定功能模块
mason make page --name login --feature auth
```

#### 生成的文件结构
```
lib/features/auth/presentation/pages/login_page.dart
```

## 🔧 实际开发场景

### 场景1：用户管理系统

```bash
# 1. 创建主功能模块
mason make feature --name user_management \
  --entity true \
  --repository true \
  --usecase true \
  --bloc true \
  --page true \
  --tests true

# 2. 创建相关页面
mason make page --name user_list --feature user_management
mason make page --name user_detail --feature user_management
mason make page --name user_edit --feature user_management

# 3. 创建数据模型
mason make model --name user_profile --feature user_management
mason make model --name user_role --feature user_management

# 4. 创建专用用例
mason make usecase --name search_users --feature user_management
mason make usecase --name export_users --feature user_management

# 5. 创建验证器
mason make validator --name user_input --feature user_management

# 6. 记录架构决策
mason make adr --name "user-management-system-design"
```

### 场景2：电商应用

```bash
# 1. 创建核心功能
mason make feature --name product_catalog \
  --entity true \
  --repository true \
  --usecase true \
  --bloc true \
  --page true

mason make feature --name shopping_cart \
  --entity true \
  --repository true \
  --usecase true \
  --bloc true \
  --page true

mason make feature --name order_management \
  --entity true \
  --repository true \
  --usecase true \
  --bloc true \
  --page true

# 2. 创建 API 客户端
mason make api_client --name payment_gateway

# 3. 创建服务
mason make service --name inventory_service
mason make service --name notification_service

# 4. 创建数据源
mason make data_source --name local_cache
mason make data_source --name remote_api
```

### 场景3：快速原型开发

```bash
# 1. 快速创建 MVP 功能
mason make feature --name prototype_feature \
  --entity true \
  --bloc false \
  --page true \
  --tests false

# 2. 后续完善
mason make bloc --name prototype_feature --feature prototype_feature
mason make repository --name prototype_feature --feature prototype_feature
mason make usecase --name get_prototype_data --feature prototype_feature
```

## 🎨 模板变量系统

所有模板支持智能变量替换：

| 变量 | 示例 | 说明 |
|------|------|------|
| `{{name}}` | `user_management` | 原始名称 |
| `{{name.pascalCase}}` | `UserManagement` | 帕斯卡命名 |
| `{{name.snakeCase}}` | `user_management` | 下划线命名 |
| `{{name.camelCase}}` | `userManagement` | 驼峰命名 |
| `{{feature}}` | `auth` | 功能模块名 |
| `{{feature.pascalCase}}` | `Auth` | 功能模块帕斯卡命名 |

## 📝 最佳实践

### 1. 命名规范
```bash
# ✅ 良好的命名
mason make feature --name user_profile
mason make feature --name order_management
mason make feature --name product_catalog

# ❌ 避免的命名
mason make feature --name userprofile
mason make feature --name UserManagement
mason make feature --name product-catalog
```

### 2. 功能模块设计
```bash
# ✅ 合理的功能划分
mason make feature --name user_authentication
mason make feature --name user_profile
mason make feature --name user_settings

# ❌ 过大的功能模块
mason make feature --name user_everything
```

### 3. 渐进式开发
```bash
# 第一步：创建基础结构
mason make feature --name new_feature --entity true --page true

# 第二步：添加业务逻辑
mason make usecase --name process_new_feature --feature new_feature
mason make bloc --name new_feature --feature new_feature

# 第三步：完善测试
mason make test --name new_feature_unit --feature new_feature --type unit
mason make test --name new_feature_widget --feature new_feature --type widget
```

## 🔧 高级配置

### 自定义模板
```bash
# 创建自定义模板
mason make feature --name custom_template --custom-template

# 使用自定义配置
mason make feature --name my_feature --config custom_config.json
```

### 批量生成
```bash
# 创建多个功能模块
for feature in auth user product order; do
  mason make feature --name $feature
done
```

### 集成到 CI/CD
```yaml
# .github/workflows/generate.yml
- name: Generate code
  run: |
    mason make feature --name ci_generated_feature
    make gen
```

## 🚀 故障排除

### 常见问题

#### 1. 模板不存在
```bash
# 检查模板列表
mason list

# 重新安装模板
mason add git://github.com/your-org/flutter-scaffold-bricks
```

#### 2. 变量替换失败
```bash
# 检查变量名是否正确
mason make feature --name correct_name

# 查看模板帮助
mason make feature --help
```

#### 3. 文件冲突
```bash
# 删除冲突文件后重新生成
rm lib/features/old_feature/
mason make feature --name new_feature
```

### 调试技巧
```bash
# 启用详细输出
mason make feature --name debug_feature --verbose

# 检查模板路径
mason doctor

# 验证生成的代码
make analyze
make test
```

## 📚 总结

Mason 代码生成系统是 Flutter Scaffold 项目的核心功能之一，它能够：

- **提高开发效率**：自动生成重复性代码
- **保证代码质量**：统一的代码结构和规范
- **促进最佳实践**：Clean Architecture 的标准实现
- **减少错误**：减少手写代码的错误率
- **便于维护**：标准化的代码结构便于后续维护

通过合理使用 Mason 模板，可以显著提升 Flutter 应用的开发效率和代码质量。