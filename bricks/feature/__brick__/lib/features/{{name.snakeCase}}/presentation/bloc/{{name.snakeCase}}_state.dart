import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/{{name.snakeCase}}.dart';

part '{{name.snakeCase}}_state.freezed.dart';

@freezed
class {{name.pascalCase}}State with _${{name.pascalCase}}State {
  const factory {{name.pascalCase}}State.initial() = _Initial;
  
  const factory {{name.pascalCase}}State.loading() = _Loading;
  
  const factory {{name.pascalCase}}State.loaded({{name.pascalCase}} {{name.camelCase}}) = _Loaded;
  
  const factory {{name.pascalCase}}State.loadedAll(List<{{name.pascalCase}}> {{name.camelCase}}s) = _LoadedAll;
  
  const factory {{name.pascalCase}}State.error(String message) = _Error;
}