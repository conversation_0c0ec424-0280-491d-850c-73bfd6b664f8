import 'package:dio/dio.dart';

/// Interceptor that automatically retries failed requests
/// Provides intelligent retry logic for network failures
class RetryInterceptor extends Interceptor {
  final int maxRetries;
  final Duration baseDelay;

  const RetryInterceptor({
    this.maxRetries = 3,
    this.baseDelay = const Duration(milliseconds: 1000),
  });

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final retryCount = err.requestOptions.extra['retryCount'] ?? 0;
    
    if (_shouldRetry(err) && retryCount < maxRetries) {
      // Increment retry count
      err.requestOptions.extra['retryCount'] = retryCount + 1;
      
      // Calculate exponential backoff delay
      final delay = baseDelay * (1 << retryCount); // 1s, 2s, 4s...
      await Future.delayed(delay);
      
      try {
        // Create a new Dio instance to avoid interceptor conflicts
        final dio = Dio(BaseOptions(
          baseUrl: err.requestOptions.baseUrl,
          connectTimeout: err.requestOptions.connectTimeout,
          receiveTimeout: err.requestOptions.receiveTimeout,
          sendTimeout: err.requestOptions.sendTimeout,
          headers: err.requestOptions.headers,
        ));
        final response = await dio.fetch(err.requestOptions);
        handler.resolve(response);
        return;
      } catch (e) {
        // If retry also fails, continue with the retry logic or fail
      }
    }
    
    // If max retries exceeded or shouldn't retry, pass the error
    handler.next(err);
  }

  /// Determines if a request should be retried based on the error type
  bool _shouldRetry(DioException err) {
    // Retry on network timeouts
    if (err.type == DioExceptionType.connectionTimeout ||
        err.type == DioExceptionType.receiveTimeout ||
        err.type == DioExceptionType.sendTimeout) {
      return true;
    }
    
    // Retry on server errors (5xx)
    if (err.response?.statusCode != null) {
      final statusCode = err.response!.statusCode!;
      return statusCode >= 500 && statusCode < 600;
    }
    
    // Don't retry on client errors (4xx) or other types
    return false;
  }
}