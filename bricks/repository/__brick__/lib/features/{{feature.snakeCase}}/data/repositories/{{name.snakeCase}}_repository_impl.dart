import 'package:fpdart/fpdart.dart';
import '../../../../../core/error/failure.dart';
import '../../../../../core/network/network_info.dart';
import '../../domain/entities/{{name.snakeCase}}.dart';
import '../../domain/repositories/{{name.snakeCase}}_repository.dart';
import '../datasources/{{name.snakeCase}}_local_data_source.dart';
import '../datasources/{{name.snakeCase}}_remote_data_source.dart';
import '../models/{{name.snakeCase}}_model.dart';

class {{name.pascalCase}}RepositoryImpl implements {{name.pascalCase}}Repository {
  final {{name.pascalCase}}RemoteDataSource remoteDataSource;
  final {{name.pascalCase}}LocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  const {{name.pascalCase}}RepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, {{name.pascalCase}}>> create{{name.pascalCase}}({{name.pascalCase}} {{name.camelCase}}) async {
    try {
      if (await networkInfo.isConnected) {
        final remote{{name.pascalCase}} = await remoteDataSource.create{{name.pascalCase}}({{name.camelCase}});
        localDataSource.create{{name.pascalCase}}({{name.camelCase}});
        return Right(remote{{name.pascalCase}});
      } else {
        final local{{name.pascalCase}} = await localDataSource.create{{name.pascalCase}}({{name.camelCase}});
        return Right(local{{name.pascalCase}});
      }
    } on Exception catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, {{name.pascalCase}}>> get{{name.pascalCase}}(String id) async {
    try {
      if (await networkInfo.isConnected) {
        final remote{{name.pascalCase}} = await remoteDataSource.get{{name.pascalCase}}(id);
        localDataSource.create{{name.pascalCase}}(remote{{name.pascalCase}});
        return Right(remote{{name.pascalCase}});
      } else {
        final local{{name.pascalCase}} = await localDataSource.get{{name.pascalCase}}(id);
        return Right(local{{name.pascalCase}});
      }
    } on Exception catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<{{name.pascalCase}}>>> getAll{{name.pascalCase}}s() async {
    try {
      if (await networkInfo.isConnected) {
        final remote{{name.pascalCase}}s = await remoteDataSource.getAll{{name.pascalCase}}s();
        localDataSource.create{{name.pascalCase}}(remote{{name.pascalCase}}s.first); // Simplified for example
        return Right(remote{{name.pascalCase}}s);
      } else {
        final local{{name.pascalCase}}s = await localDataSource.getAll{{name.pascalCase}}s();
        return Right(local{{name.pascalCase}}s);
      }
    } on Exception catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, {{name.pascalCase}}>> update{{name.pascalCase}}({{name.pascalCase}} {{name.camelCase}}) async {
    try {
      if (await networkInfo.isConnected) {
        final remote{{name.pascalCase}} = await remoteDataSource.update{{name.pascalCase}}({{name.camelCase}});
        localDataSource.update{{name.pascalCase}}({{name.camelCase}});
        return Right(remote{{name.pascalCase}});
      } else {
        final local{{name.pascalCase}} = await localDataSource.update{{name.pascalCase}}({{name.camelCase}});
        return Right(local{{name.pascalCase}});
      }
    } on Exception catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> delete{{name.pascalCase}}(String id) async {
    try {
      if (await networkInfo.isConnected) {
        await remoteDataSource.delete{{name.pascalCase}}(id);
        localDataSource.delete{{name.pascalCase}}(id);
        return const Right(null);
      } else {
        await localDataSource.delete{{name.pascalCase}}(id);
        return const Right(null);
      }
    } on Exception catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}