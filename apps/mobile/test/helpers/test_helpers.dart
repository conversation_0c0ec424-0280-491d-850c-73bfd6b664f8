import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mocktail/mocktail.dart';

import 'package:flutter_scaffold_mobile/features/auth/presentation/bloc/auth_bloc.dart';

class MockAuthBloc extends Mock implements AuthBloc {
  final _completer = Completer<void>();
  
  @override
  Future<void> close() {
    if (!_completer.isCompleted) {
      _completer.complete();
    }
    return _completer.future;
  }
}

/// Test helper to create a widget with proper providers and ignore layout overflow errors
Widget createTestWidget(Widget child, {MockAuthBloc? authBloc}) {
  final mockAuthBloc = authBloc ?? MockAuthBloc();
  
  return MaterialApp(
    home: BlocProvider<AuthBloc>(
      create: (_) => mockAuthBloc,
      child: child,
    ),
  );
}

/// Pump widget and ignore layout overflow errors
Future<void> pumpWidgetWithoutOverflowErrors(
  WidgetTester tester,
  Widget widget,
) async {
  // Ignore overflow errors during testing
  FlutterError.onError = (FlutterErrorDetails details) {
    final exception = details.exception;
    final isOverflowError = exception is FlutterError &&
        exception.message.contains('overflowed by');
    
    if (!isOverflowError) {
      FlutterError.presentError(details);
    }
  };
  
  await tester.pumpWidget(widget);
  
  // Restore error handling
  FlutterError.onError = FlutterError.presentError;
}

/// Setup mock AuthBloc with default behavior
void setupMockAuthBloc(MockAuthBloc mockAuthBloc) {
  when(() => mockAuthBloc.state).thenReturn(const AuthState());
  when(() => mockAuthBloc.stream).thenAnswer((_) => Stream.value(const AuthState()));
}
