part of '{{name.snakeCase()}}_bloc.dart';

@freezed
class {{name.pascalCase()}}Event with _${{name.pascalCase()}}Event {
  const factory {{name.pascalCase()}}Event.started() = _Started;
  const factory {{name.pascalCase()}}Event.load{{entity.pascalCase()}}() = _Load{{entity.pascalCase()}};
  const factory {{name.pascalCase()}}Event.create{{entity.pascalCase()}}({{entity.pascalCase()}} {{entity.camelCase()}}) = _Create{{entity.pascalCase()}};
  const factory {{name.pascalCase()}}Event.update{{entity.pascalCase()}}({{entity.pascalCase()}} {{entity.camelCase()}}) = _Update{{entity.pascalCase()}};
  const factory {{name.pascalCase()}}Event.delete{{entity.pascalCase()}}(String id) = _Delete{{entity.pascalCase()}};
}