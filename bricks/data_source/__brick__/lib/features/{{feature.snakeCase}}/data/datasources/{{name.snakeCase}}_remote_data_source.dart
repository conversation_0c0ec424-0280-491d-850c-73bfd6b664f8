import 'package:dio/dio.dart';
import '../../domain/entities/{{name.snakeCase}}.dart';

class {{name.pascalCase}}RemoteDataSource {
  final Dio dio;

  {{name.pascalCase}}RemoteDataSource({required this.dio});

  Future<{{name.pascalCase}}> create{{name.pascalCase}}({{name.pascalCase}} {{name.camelCase}}) async {
    // TODO: Implement API call to create {{name.camelCase}}
    throw UnimplementedError('Implement create{{name.pascalCase}} API call');
  }

  Future<{{name.pascalCase}}> get{{name.pascalCase}}(String id) async {
    // TODO: Implement API call to get {{name.camelCase}}
    throw UnimplementedError('Implement get{{name.pascalCase}} API call');
  }

  Future<List<{{name.pascalCase}}>> getAll{{name.pascalCase}}s() async {
    // TODO: Implement API call to get all {{name.camelCase}}s
    throw UnimplementedError('Implement getAll{{name.pascalCase}}s API call');
  }

  Future<{{name.pascalCase}}> update{{name.pascalCase}}({{name.pascalCase}} {{name.camelCase}}) async {
    // TODO: Implement API call to update {{name.camelCase}}
    throw UnimplementedError('Implement update{{name.pascalCase}} API call');
  }

  Future<void> delete{{name.pascalCase}}(String id) async {
    // TODO: Implement API call to delete {{name.camelCase}}
    throw UnimplementedError('Implement delete{{name.pascalCase}} API call');
  }
}