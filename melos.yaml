name: flutter_scaffold

packages:
  - apps/**
  - packages/**

scripts:
  analyze:
    run: |
      melos exec -- \
        flutter analyze --fatal-infos
    description: Analyze all packages

  format:
    run: |
      melos exec -- \
        dart format --set-exit-if-changed .
    description: Format all packages

  test:
    run: |
      melos exec -- \
        flutter test --coverage
    description: Run all tests

  gen:
    run: |
      melos exec -- \
        dart run build_runner build --delete-conflicting-outputs
    description: Run code generation

  bootstrap:
    run: |
      melos bootstrap
    description: Bootstrap all packages

  clean:
    run: |
      melos exec -- \
        flutter clean
    description: Clean all packages

  upgrade:
    run: |
      melos exec -- \
        flutter pub upgrade
    description: Upgrade all dependencies

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.10.0"