{
    "version": "0.2",
    "language": "en",
    "caseSensitive": true,
    "allowCompoundWords": true,
    "$schema": "https://raw.githubusercontent.com/streetsidesoftware/cspell/main/cspell.schema.json",
    "words": [
      // Flutter/Dart core types and keywords
      "async", "await", "typedef", "mixin", "enum", "const", "final", "var", "late",
      "required", "nullable", "non", "null", "void", "dynamic", "num", "int", "double",
      "bool", "String", "List", "Map", "Set", "Iterable", "Future", "Stream", "Duration",
      "DateTime", "Uri", "RegExp", "StringBuffer", "Completer", "StreamController",

      // Flutter framework core
      "flutter", "dart", "dartlang", "pubspec", "pubdev", "dartpad", "dartfmt", "dartdoc",
      "StatelessWidget", "StatefulWidget", "InheritedWidget", "BuildContext", "Widget",
      "State", "setState", "initState", "dispose", "didUpdateWidget", "didChangeDependencies",
      "build", "createState", "mounted", "context",

      // Flutter UI components
      "Scaffold", "AppBar", "FloatingActionButton", "Drawer", "BottomNavigationBar",
      "TabBar", "TabBarView", "ListView", "GridView", "Column", "Row", "Stack", "Positioned",
      "Container", "Padding", "Margin", "SizedBox", "Expanded", "Flexible", "Wrap", "Align",
      "Center", "FittedBox", "AspectRatio", "Card", "Chip", "Divider", "Spacer",
      "TextField", "TextFormField", "DropdownButton", "Checkbox", "Radio", "Switch", "Slider",
      "RaisedButton", "FlatButton", "IconButton", "OutlinedButton", "TextButton", "ElevatedButton",
      "GestureDetector", "InkWell", "Hero", "AnimatedContainer", "AnimatedOpacity",
      "FadeTransition", "SlideTransition", "ScaleTransition", "RotationTransition",

      // Flutter navigation and routing
      "Navigator", "Route", "MaterialPageRoute", "CupertinoPageRoute", "PageRouteBuilder",
      "ModalRoute", "PopupRoute", "GoRouter", "GoRoute", "GoRouterDelegate", "RouteInformation",
      "RouterDelegate", "RouteInformationParser", "pushNamed", "pushReplacementNamed",
      "popAndPushNamed", "canPop", "maybePop", "pushAndRemoveUntil",

      // Flutter state management
      "Provider", "ChangeNotifier", "ValueNotifier", "ValueListenable", "StreamBuilder",
      "FutureBuilder", "AnimatedBuilder", "BlocProvider", "BlocBuilder", "BlocListener",
      "BlocConsumer", "MultiBlocProvider", "MultiBlocListener", "RepositoryProvider",
      "Cubit", "Bloc", "BlocBase", "Emitter", "Transition", "Change",

      // Flutter testing
      "testWidgets", "WidgetTester", "pumpWidget", "pumpAndSettle", "pump", "binding",
      "TestWidgetsFlutterBinding", "ensureInitialized", "mockito", "mocktail", "bloc_test",
      "whenListen", "blocTest", "emitsInOrder", "emitsInAnyOrder", "emitsDone", "emitsError",
      "verify", "verifyNever", "verifyInOrder", "verifyZeroInteractions", "clearInteractions",

      // Flutter packages and plugins
      "cupertino", "material", "services", "foundation", "painting", "gestures", "rendering",
      "widgets", "animation", "scheduler", "semantics", "physics", "http", "dio", "retrofit",
      "json_annotation", "json_serializable", "freezed", "injectable", "get_it", "auto_route",
      "shared_preferences", "sqflite", "hive", "drift", "firebase", "firestore", "crashlytics",
      "analytics", "admob", "in_app_purchase", "camera", "image_picker", "permission_handler",
      "geolocator", "url_launcher", "webview_flutter", "video_player", "audioplayers",

      // Common programming terms
      "callback", "listener", "notifier", "observable", "subscription",
      "dispose", "lifecycle", "memoization", "debounce", "throttle", "singleton", "factory",
      "repository", "usecase", "entity", "dto", "mapper", "adapter", "decorator", "facade",
      "strategy", "observer", "command", "builder", "prototype", "proxy", "chain",

      // API and networking
      "api", "rest", "graphql", "websocket", "http", "https", "json", "xml", "yaml",
      "oauth", "jwt", "bearer", "auth", "authentication", "authorization", "middleware",
      "interceptor", "header", "cookie", "session", "token", "refresh", "endpoint",
      "baseurl", "timeout", "retry", "cache", "etag", "cors", "csrf",

      // Database and storage
      "database", "sql", "nosql", "sqlite", "postgresql", "mysql", "mongodb", "redis",
      "firestore", "realtime", "offline", "sync", "migration", "schema", "index", "query",
      "transaction", "rollback", "commit", "crud", "orm", "dao", "repository",

      // Development tools and concepts
      "linter", "formatter", "analyzer", "debugger", "profiler", "inspector", "devtools",
      "hotreload", "hotrestart", "build", "release", "debug", "profile", "obfuscate",
      "minify", "treeshake", "aot", "jit", "snapshot", "isolate", "platform", "channel",

      // Placeholder names
      "foo", "bar", "baz", "qux", "lorem", "ipsum", "placeholder", "example", "demo", "test"
    ],
    "ignoreWords": [
      // Add words here that you want to ignore for this specific project.
      // For example, a company-specific acronym or a short variable name.
      "gatspy", "scaffold", "xingcheng"
    ],
    "ignorePaths": [
      "**/node_modules/**",
      "**/vendor/**",
      "**/dist/**",
      "**/build/**",
      "**/.git/**",
      "**/*.lock",
      "**/pubspec.lock",
      "**/.dart_tool/**",
      "**/.flutter-plugins",
      "**/.flutter-plugins-dependencies",
      "**/ios/Pods/**",
      "**/android/.gradle/**",
      "**/android/app/build/**",
      "**/web/build/**",
      "**/coverage/**",
      "**/*.g.dart",
      "**/*.freezed.dart",
      "**/*.config.dart",
      "**/*.gr.dart",
      "**/*.mocks.dart"
    ],
    "languageSettings": [
      {
        "languageId": "dart",
        "dictionaries": ["dart", "flutter", "en-us"],
        "ignoreRegExpList": [
          // Ignore hex color values
          "0x[0-9a-fA-F]{8}",
          "0x[0-9a-fA-F]{6}",
          // Ignore import paths in single quotes
          "'[a-zA-Z0-9_./:-]+\\.dart'",
          "'package:[a-zA-Z0-9_./:-]+\\.dart'",
          // Ignore asset paths
          "'assets/[a-zA-Z0-9_./:-]+\\.[a-zA-Z0-9]+'"
        ]
      },
      {
        "languageId": "yaml",
        "dictionaries": ["flutter", "en-us"],
        "ignoreRegExpList": [
          // Ignore version numbers
          "\\^?[0-9]+\\.[0-9]+\\.[0-9]+",
          // Ignore package names with underscores
          "[a-z_]+:"
        ]
      },
      {
        "languageId": "json",
        "dictionaries": ["flutter", "en-us"]
      }
    ],
    "dictionaries": ["app", "companies", "software-terms", "misc", "dart", "flutter"],
    "dictionaryDefinitions": [
      {
        "name": "app",
        "path": "./app-dictionary.txt",
        "addWords": true,
        "description": "Project-specific terms and custom words"
      },
      {
        "name": "dart",
        "description": "Dart language specific terms"
      },
      {
        "name": "flutter",
        "description": "Flutter framework specific terms"
      }
    ],
    "overrides": [
      {
        "filename": "**/pubspec.yaml",
        "dictionaries": ["flutter", "app"],
        "ignoreRegExpList": [
          // Ignore version constraints
          "\\^?[0-9]+\\.[0-9]+\\.[0-9]+[+-]?[a-zA-Z0-9]*",
          // Ignore git URLs
          "https?://[a-zA-Z0-9.-]+/[a-zA-Z0-9._/-]+\\.git"
        ]
      },
      {
        "filename": "**/*.dart",
        "dictionaries": ["dart", "flutter", "app"],
        "ignoreRegExpList": [
          // Ignore generated file headers
          "// GENERATED CODE - DO NOT MODIFY BY HAND",
          "// coverage:ignore-file",
          "// ignore_for_file:",
          // Ignore TODO comments with usernames
          "// TODO\\([a-zA-Z0-9_-]+\\):"
        ]
      },
      {
        "filename": "**/test/**/*.dart",
        "dictionaries": ["dart", "flutter", "app"],
        "words": [
          "testWidgets", "pumpWidget", "pumpAndSettle", "mockito", "mocktail",
          "whenListen", "blocTest", "verify", "verifyNever", "clearInteractions"
        ]
      }
    ]
  }