# Flutter Scaffold - Makefile
# Quick commands for development workflow

.PHONY: setup bootstrap clean test build run dev

# Environment variables
FLUTTER_VERSION ?= 3.32.5
APP_DIR := apps/mobile

# Setup development environment
setup:
	@echo "Setting up Flutter Scaffold development environment..."
	@echo "Installing Melos..."
	dart pub global activate melos
	@echo "Installing Mason..."
	dart pub global activate mason_cli
	@echo "Installing Patrol..."
	dart pub global activate patrol_cli
	@echo "Bootstrap packages..."
	melos bootstrap
	@echo "Setup complete! 🚀"

# Bootstrap all packages
bootstrap:
	melos bootstrap

# Clean all packages
clean:
	melos run clean

# Run tests for all packages
test:
	melos run test

# Run tests with coverage
test-coverage:
	melos run test --coverage

# Generate code for all packages
gen:
	melos run gen

# Format all packages
format:
	melos run format

# Analyze all packages
analyze:
	melos run analyze

# Run development server
run-dev:
	cd $(APP_DIR) && flutter run --flavor development -t lib/main_development.dart

# Run staging server
run-stag:
	cd $(APP_DIR) && flutter run --flavor staging -t lib/main_staging.dart

# Run production server
run-prod:
	cd $(APP_DIR) && flutter run --flavor production -t lib/main_production.dart

# Build APK for development
build-dev:
	cd $(APP_DIR) && flutter build apk --flavor development -t lib/main_development.dart

# Build APK for staging
build-stag:
	cd $(APP_DIR) && flutter build apk --flavor staging -t lib/main_staging.dart

# Build APK for production
build-prod:
	cd $(APP_DIR) && flutter build apk --flavor production -t lib/main_production.dart

# Build iOS for development
build-ios-dev:
	cd $(APP_DIR) && flutter build ios --flavor development -t lib/main_development.dart --no-codesign

# Build iOS for staging
build-ios-stag:
	cd $(APP_DIR) && flutter build ios --flavor staging -t lib/main_staging.dart --no-codesign

# Build iOS for production
build-ios-prod:
	cd $(APP_DIR) && flutter build ios --flavor production -t lib/main_production.dart --no-codesign

# Generate new feature
feature:
	@read -p "Enter feature name: " name; \
	mason make feature --name $$name

# Generate new ADR
adr:
	@read -p "Enter ADR title: " title; \
	mason make adr --name "$$title"

# Update dependencies
upgrade:
	melos run upgrade

# Check for outdated packages
outdated:
	melos exec -- flutter pub outdated

# Run integration tests
integration:
	cd $(APP_DIR) && patrol test integration_test

# Help
help:
	@echo "Available commands:"
	@echo "  setup      - Setup development environment"
	@echo "  bootstrap  - Bootstrap all packages"
	@echo "  clean      - Clean all packages"
	@echo "  test       - Run tests for all packages"
	@echo "  gen        - Generate code for all packages"
	@echo "  format     - Format all packages"
	@echo "  analyze    - Analyze all packages"
	@echo "  run-dev    - Run development server"
	@echo "  run-stag   - Run staging server"
	@echo "  run-prod   - Run production server"
	@echo "  build-dev  - Build APK for development"
	@echo "  build-stag - Build APK for staging"
	@echo "  build-prod - Build APK for production"
	@echo "  feature    - Generate new feature"
	@echo "  adr        - Generate new ADR"
	@echo "  upgrade    - Update dependencies"
	@echo "  outdated   - Check for outdated packages"
	@echo "  integration - Run integration tests"