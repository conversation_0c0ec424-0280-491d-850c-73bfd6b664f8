<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.idea" />
      <excludeFolder url="file://$MODULE_DIR$/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/build" />
      <excludeFolder url="file://$MODULE_DIR$/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/example/.idea" />
      <excludeFolder url="file://$MODULE_DIR$/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/example/android/.gradle" />
      <excludeFolder url="file://$MODULE_DIR$/example/android/.idea" />
      <excludeFolder url="file://$MODULE_DIR$/example/ios/Flutter" />
      <excludeFolder url="file://$MODULE_DIR$/example/ios/Pods" />
      <excludeFolder url="file://$MODULE_DIR$/example/ios/.symlinks" />
      <excludeFolder url="file://$MODULE_DIR$/example/macos/Flutter" />
      <excludeFolder url="file://$MODULE_DIR$/example/macos/Pods" />
      <excludeFolder url="file://$MODULE_DIR$/example/macos/.symlinks" />
    </content>
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Dart SDK" level="project" />
    <orderEntry type="library" name="Flutter Plugins" level="project" />
    <orderEntry type="library" name="Dart Packages" level="project" />
  </component>
</module>
