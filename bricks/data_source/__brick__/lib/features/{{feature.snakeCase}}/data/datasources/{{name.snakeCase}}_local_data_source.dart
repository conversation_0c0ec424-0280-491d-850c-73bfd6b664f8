import 'package:drift/drift.dart';
import '../../domain/entities/{{name.snakeCase}}.dart';

class {{name.pascalCase}}LocalDataSource {
  // TODO: Add database dependency
  // final AppDatabase database;

  {{name.pascalCase}}LocalDataSource(); // Add database parameter

  Future<{{name.pascalCase}}> create{{name.pascalCase}}({{name.pascalCase}} {{name.camelCase}}) async {
    // TODO: Implement local database insert
    throw UnimplementedError('Implement create{{name.pascalCase}} database operation');
  }

  Future<{{name.pascalCase}}> get{{name.pascalCase}}(String id) async {
    // TODO: Implement local database select
    throw UnimplementedError('Implement get{{name.pascalCase}} database operation');
  }

  Future<List<{{name.pascalCase}}>> getAll{{name.pascalCase}}s() async {
    // TODO: Implement local database select all
    throw UnimplementedError('Implement getAll{{name.pascalCase}}s database operation');
  }

  Future<{{name.pascalCase}}> update{{name.pascalCase}}({{name.pascalCase}} {{name.camelCase}}) async {
    // TODO: Implement local database update
    throw UnimplementedError('Implement update{{name.pascalCase}} database operation');
  }

  Future<void> delete{{name.pascalCase}}(String id) async {
    // TODO: Implement local database delete
    throw UnimplementedError('Implement delete{{name.pascalCase}} database operation');
  }
}