import 'package:dio/dio.dart';
import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Enhanced cache interceptor with network-aware caching
class CacheInterceptor extends Interceptor {
  late final CacheOptions _cacheOptions;
  late final DioCacheInterceptor _cacheInterceptor;
  final Connectivity _connectivity = Connectivity();

  CacheInterceptor() {
    _cacheOptions = CacheOptions(
      store: MemCacheStore(),
      policy: CachePolicy.request,
      maxStale: const Duration(days: 7),
      priority: CachePriority.normal,
      keyBuilder: CacheOptions.defaultCacheKeyBuilder,
      allowPostMethod: false,
    );

    _cacheInterceptor = DioCacheInterceptor(options: _cacheOptions);
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // Check network connectivity
    final connectivityResult = await _connectivity.checkConnectivity();
    final isOffline = connectivityResult.contains(ConnectivityResult.none);

    if (_shouldCache(options)) {
      // If offline, force cache usage
      if (isOffline) {
        options.extra.addAll(_cacheOptions.copyWith(
          policy: CachePolicy.request,
          maxStale: const Duration(days: 30), // Allow stale cache when offline
        ).toExtra());
      } else {
        // Online: use configured cache policy
        options.extra.addAll(_cacheOptions.toExtra());
      }
    }

    _cacheInterceptor.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    _cacheInterceptor.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // On network error, try to serve from cache
    if (_isNetworkError(err) && _shouldCache(err.requestOptions)) {
      try {
        final cachedResponse = await _tryGetCachedResponse(err.requestOptions);
        if (cachedResponse != null) {
          handler.resolve(cachedResponse);
          return;
        }
      } catch (e) {
        // Failed to get cached response, continue with error
      }
    }

    _cacheInterceptor.onError(err, handler);
  }

  bool _shouldCache(RequestOptions options) {
    // Only cache GET requests
    if (options.method.toLowerCase() != 'get') return false;

    // Don't cache auth-related endpoints
    if (options.path.contains('/auth/')) return false;

    // Don't cache real-time data endpoints
    if (options.path.contains('/realtime/')) return false;

    // Don't cache user-specific sensitive data
    if (options.path.contains('/user/private/')) return false;

    return true;
  }

  bool _isNetworkError(DioException err) {
    return err.type == DioExceptionType.connectionTimeout ||
           err.type == DioExceptionType.receiveTimeout ||
           err.type == DioExceptionType.connectionError;
  }

  Future<Response?> _tryGetCachedResponse(RequestOptions options) async {
    try {
      // Try to get cached response with extended stale time
      // Use a simple key generation for compatibility
      final cacheKey = '${options.method}_${options.uri}';
      final cachedData = await _cacheOptions.store?.get(cacheKey);

      if (cachedData != null) {
        return Response(
          requestOptions: options,
          data: cachedData.content,
          statusCode: 200,
          headers: Headers.fromMap({'x-cache': ['HIT']}),
        );
      }
    } catch (e) {
      // Ignore cache errors
    }
    return null;
  }
}