# Mason Bricks Templates

This directory contains Mason brick templates for code generation.

## Important Notes

⚠️ **Template Files**: Files in `__brick__` directories are Mason templates, not regular Dart files. They contain template syntax like `{{name.snakeCase}}` which will be replaced during code generation.

## Available Bricks

### api_client
Generates API client classes using Retrofit and Dio.

**Usage:**
```bash
mason make api_client --name user
```

**Generated files:**
- `lib/core/network/user_api_client.dart`
- `lib/core/network/user_api_client.g.dart` (after code generation)

## Development Guidelines

1. **Do not edit generated files directly** - they will be overwritten
2. **Template syntax** uses double curly braces: `{{variable}}`
3. **Dependencies** are resolved in the target project, not in the brick template
4. **IDE warnings** in `__brick__` directories are expected and should be ignored

## Code Generation Workflow

1. Create/modify brick template in `__brick__` directory
2. Run `mason make <brick_name>` to generate code
3. Run `dart run build_runner build` to generate additional files (*.g.dart)

## Troubleshooting

If you see Dart analysis errors in brick templates:
- These are expected and can be safely ignored
- The errors occur because templates contain Mason syntax, not valid Dart
- Generated code will be valid Dart after template processing
