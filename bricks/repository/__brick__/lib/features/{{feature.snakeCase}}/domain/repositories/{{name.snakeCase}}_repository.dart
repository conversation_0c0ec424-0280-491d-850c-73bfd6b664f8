import 'package:fpdart/fpdart.dart';
import '../../../../../core/error/failure.dart';
import '../../domain/entities/{{name.snakeCase}}.dart';

abstract interface class {{name.pascalCase}}Repository {
  Future<Either<Failure, {{name.pascalCase}}>> create{{name.pascalCase}}({{name.pascalCase}} {{name.camelCase}});
  
  Future<Either<Failure, {{name.pascalCase}}>> get{{name.pascalCase}}(String id);
  
  Future<Either<Failure, List<{{name.pascalCase}}>>> getAll{{name.pascalCase}}s();
  
  Future<Either<Failure, {{name.pascalCase}}>> update{{name.pascalCase}}({{name.pascalCase}} {{name.camelCase}});
  
  Future<Either<Failure, void>> delete{{name.pascalCase}}(String id);
}